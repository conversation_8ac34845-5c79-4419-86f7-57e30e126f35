{"__meta": {"id": "Xad70d418f4e413a5d69507eb564e924e", "datetime": "2025-07-20 08:42:38", "utime": 1752975758.809931, "method": "GET", "uri": "/dokumen-kapal/download/5", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.537224, "end": 1752975758.809955, "duration": 3.272730827331543, "duration_str": "3.27s", "measures": [{"label": "Booting", "start": **********.537224, "relative_start": 0, "end": **********.777463, "relative_end": **********.777463, "duration": 0.24023890495300293, "duration_str": "240ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.777483, "relative_start": 0.24025893211364746, "end": 1752975758.809958, "relative_end": 3.0994415283203125e-06, "duration": 3.032474994659424, "duration_str": "3.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 77505800, "peak_usage_str": "74MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "dokumen.pdf.list", "param_count": null, "params": [], "start": **********.870433, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/pdf/list.blade.phpdokumen.pdf.list", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fpdf%2Flist.blade.php&line=1", "ajax": false, "filename": "list.blade.php", "line": "?"}}, {"name": "dokumen.pdf.partials.dokumen-table", "param_count": null, "params": [], "start": **********.97811, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/pdf/partials/dokumen-table.blade.phpdokumen.pdf.partials.dokumen-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fpdf%2Fpartials%2Fdokumen-table.blade.php&line=1", "ajax": false, "filename": "dokumen-table.blade.php", "line": "?"}}, {"name": "dokumen.pdf.partials.dokumen-table", "param_count": null, "params": [], "start": 1752975756.064944, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/pdf/partials/dokumen-table.blade.phpdokumen.pdf.partials.dokumen-table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fpdf%2Fpartials%2Fdokumen-table.blade.php&line=1", "ajax": false, "filename": "dokumen-table.blade.php", "line": "?"}}]}, "route": {"uri": "GET dokumen-kapal/download/{kapal}", "middleware": "web, auth, Closure", "controller": "App\\Http\\Controllers\\DokumenKapalController@downloadList", "namespace": null, "prefix": "/dokumen-kapal", "where": [], "as": "dokumen.download", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=279\" onclick=\"\">app/Http/Controllers/DokumenKapalController.php:279-350</a>"}, "queries": {"nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01833, "accumulated_duration_str": "18.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.823638, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.827813, "duration": 0.01602, "duration_str": "16.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 87.398}, {"sql": "select * from `kapal` where `id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 57}], "start": **********.848311, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "gema_kapal", "explain": null, "start_percent": 87.398, "width_percent": 2.837}, {"sql": "select * from `kapal` where `jenis_kapal_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 294}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.849991, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:294", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 294}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=294", "ajax": false, "filename": "DokumenKapalController.php", "line": "294"}, "connection": "gema_kapal", "explain": null, "start_percent": 90.235, "width_percent": 1.8}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 4 and `dokumen_kapal`.`kapal_id` is not null order by `created_at` asc", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 303}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.852715, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:303", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 303}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=303", "ajax": false, "filename": "DokumenKapalController.php", "line": "303"}, "connection": "gema_kapal", "explain": null, "start_percent": 92.035, "width_percent": 3.928}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 5 and `dokumen_kapal`.`kapal_id` is not null order by `created_at` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 323}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.857406, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:323", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 323}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=323", "ajax": false, "filename": "DokumenKapalController.php", "line": "323"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.963, "width_percent": 4.037}]}, "models": {"data": {"App\\Models\\DokumenKapal": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 39, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dokumen-kapal/download/5\"\n]"}, "request": {"path_info": "/dokumen-kapal/download/5", "status_code": "<pre class=sf-dump id=sf-dump-1115741707 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1115741707\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/pdf", "request_query": "<pre class=sf-dump id=sf-dump-1975087601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1975087601\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1984114633 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1984114633\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2135505801 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkNzSFlKZXhjT1ovOXdVdEUxMlhJbXc9PSIsInZhbHVlIjoieFlzNUxFTUJZd09CaytKZ25ZNWNwMHM4ejNBNTBWR1dYamFjZVNrcU1jZGJVSXJQR21DSVRHUVlNMDRZckFYclJBU3hWZ0hNVXVMWThNVFM3c0w0REhyWUdGMVFDYldOWmxzR21WR0wvTU8yOVc0cHNGbms5Q0lWSXVsOVlnK1kiLCJtYWMiOiJkMzE4MjIxYmFlODEyODZmOTY2OWJjOWQ0M2M1NDQwNGQ4ZmI2MjNlM2RlYmQwMmZiZWUzNzYxZGYxNzQ4M2JlIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImE3OW9PNjdMc0hxTjdMMjcyN3hoWFE9PSIsInZhbHVlIjoiU2VIRGhrVENCeWRYZkpXOXZqbFEvWGFZcmdBdUYxalNpYWtuRTM3c3lJc1l6V25xVVFna1VpN21lRW14dEM2TE9OQ2ErdmFLekw0MjVWaG1VTnJDSERKdjQ5Q21GZ3FsTUV4RWtQZVQ2VUcyVHpyc3VTeTJ0NW9vY0dzckE0NEMiLCJtYWMiOiIwYTA1NTI1MmZjOTc5YmY2ZmU2Y2VlODlhN2I1MTY4YzI4MzNlZjY4ZWMzZTFjNzhmZTg0OGNmYTk4NjkxMTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135505801\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1097534871 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z47ckW1sW3vByhJlsabzz4Jt9SLwE6fzIeTpobGt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097534871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-569208862 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">application/pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-disposition</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">inline; filename=dokumen.pdf</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:42:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlozeXprOEZxWGlXU2JEMTd2OW9oanc9PSIsInZhbHVlIjoiM2NDSzhIUG9ZT3hPOU1QMURyS0ZYeXlUV1R5ZnhxdFpaRnpWR3ZhN0lycjNDdDZ2Ym9ZRldxdXl2dEpRcXR3c05kaSs3dmhjUHpGOGp0bWNlWDlxVVRENy9ERzlBLzRZRS8zcEZubFR2aUZxb08xdXZSUXQ5RHAzVzFyY2U2NDgiLCJtYWMiOiJhMzM5YjY3ZTY4YTcxZGYxY2JhZTRjZTRjZjJjOWZkZDFmZTUwYWRmYzY2NmU0MDk2OWYyYTZlYTZiNGE1NTg3IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImZZVnZHcmszRjR1UGFZU0JPdmovNFE9PSIsInZhbHVlIjoid2UzcWwyMUR5NDIzVHNwV0hLZTlSMnhBV0NDcHVIN0IzeFFWek1RZ3lRMlJyV2NqanVCdEF6M1RaQkZFVTBtQjErMVBBYzBzM0dUZ2d3TUtrZnQ3aU96OHRqS0RQS1RSbGFqamlPUmdzSDF2dFVwb2hLNXBaTnVXZ05Ya1ZuWEciLCJtYWMiOiIxZmI2YzBhYzg5MzkwYTVhYWMxZDgyODZmYjNjYTliNTZiYmYxMTVjM2UxNjYxNjU1ZWUxZDU5NzQwMWQwM2Q3IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlozeXprOEZxWGlXU2JEMTd2OW9oanc9PSIsInZhbHVlIjoiM2NDSzhIUG9ZT3hPOU1QMURyS0ZYeXlUV1R5ZnhxdFpaRnpWR3ZhN0lycjNDdDZ2Ym9ZRldxdXl2dEpRcXR3c05kaSs3dmhjUHpGOGp0bWNlWDlxVVRENy9ERzlBLzRZRS8zcEZubFR2aUZxb08xdXZSUXQ5RHAzVzFyY2U2NDgiLCJtYWMiOiJhMzM5YjY3ZTY4YTcxZGYxY2JhZTRjZTRjZjJjOWZkZDFmZTUwYWRmYzY2NmU0MDk2OWYyYTZlYTZiNGE1NTg3IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImZZVnZHcmszRjR1UGFZU0JPdmovNFE9PSIsInZhbHVlIjoid2UzcWwyMUR5NDIzVHNwV0hLZTlSMnhBV0NDcHVIN0IzeFFWek1RZ3lRMlJyV2NqanVCdEF6M1RaQkZFVTBtQjErMVBBYzBzM0dUZ2d3TUtrZnQ3aU96OHRqS0RQS1RSbGFqamlPUmdzSDF2dFVwb2hLNXBaTnVXZ05Ya1ZuWEciLCJtYWMiOiIxZmI2YzBhYzg5MzkwYTVhYWMxZDgyODZmYjNjYTliNTZiYmYxMTVjM2UxNjYxNjU1ZWUxZDU5NzQwMWQwM2Q3IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569208862\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1726543605 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://sikapal.test/dokumen-kapal/download/5</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726543605\", {\"maxDepth\":0})</script>\n"}}