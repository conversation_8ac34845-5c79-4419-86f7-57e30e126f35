{"__meta": {"id": "X24e85716b2d5d007fa6a58a1e3b2bc40", "datetime": "2025-07-20 08:27:05", "utime": 1752974825.523317, "method": "GET", "uri": "/dokumen-kapal/view/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[08:27:04] LOG.info: Mencari kapal dengan ID: 4", "message_html": null, "is_string": false, "label": "info", "time": **********.814228, "xdebug_link": null, "collector": "log"}, {"message": "[08:27:04] LOG.info: <PERSON><PERSON> di<PERSON>n {\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.817165, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.549243, "end": 1752974825.523346, "duration": 0.9741029739379883, "duration_str": "974ms", "measures": [{"label": "Booting", "start": **********.549243, "relative_start": 0, "end": **********.744898, "relative_end": **********.744898, "duration": 0.19565510749816895, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.744914, "relative_start": 0.19567108154296875, "end": 1752974825.523349, "relative_end": 3.0994415283203125e-06, "duration": 0.7784349918365479, "duration_str": "778ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27708416, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "dokumen.show", "param_count": null, "params": [], "start": **********.828126, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.phpdokumen.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1752974825.516764, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": 1752974825.517809, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": 1752974825.519088, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dokumen-kapal/view/{kapal}", "middleware": "web, auth, admin.kantor, Closure", "controller": "App\\Http\\Controllers\\DokumenKapalController@show", "namespace": null, "prefix": "/dokumen-kapal", "where": [], "as": "dokumen.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=32\" onclick=\"\">app/Http/Controllers/DokumenKapalController.php:32-48</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01619, "accumulated_duration_str": "16.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.787269, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.791368, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 89.809}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.815048, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:41", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=41", "ajax": false, "filename": "DokumenKapalController.php", "line": "41"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.809, "width_percent": 3.582}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 4 and `dokumen_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 230}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.834081, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "dokumen.show:230", "source": {"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=230", "ajax": false, "filename": "show.blade.php", "line": "230"}, "connection": "gema_kapal", "explain": null, "start_percent": 93.391, "width_percent": 6.609}]}, "models": {"data": {"App\\Models\\DokumenKapal": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}}, "count": 25, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dokumen-kapal/view/2NaDK50q4p\"\n]"}, "request": {"path_info": "/dokumen-kapal/view/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-1382050147 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1382050147\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-349837433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-349837433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-225716008 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-225716008\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-237296111 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://sikapal.test/admin/kantor/kapal</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Img3cUQyRWpCdmdZV1JHK2dWcG9xM2c9PSIsInZhbHVlIjoiQ2hVYjZXQ09BZloyQnRHZ3dnNzNqcDBPdUtveWxmSUpOWTFHa0h4akI2TG1SeHRkLzdJMmlodUNNeWlWWi9nWFRDc0J1dzVmUzk1YWdtVk5KMmZiUWdKNllFb2lvUjBrTU9zd0JWRmFsOEdLb0xTZ0pNRzFqalcrN3Fyckx0cjEiLCJtYWMiOiIyOGQwZmMyYzhkNDZkZDNmYmFiODFlOTViYWJjNTU3NmUyMjVkNzI1MGY1Y2RiYzcyNWZmMGFkYjg1NDdhOTAzIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IjRmc1llem5SVENEZlU4VmpQM3lEQ0E9PSIsInZhbHVlIjoiSTBXK200RE1tYnVOcEpNaUpOWEJxRU9BZGdTdW9Gdnlxc3RKYlJDVmxDUTNsSXZIaUMzSVQzRTI1dTB0M3pTMlBkSWYyVFV4cEFieDdsQmp1MVRoKzNtTUoyMUg2eDM5Sk0xc3NuRWFUTFBjRGo1cVNkQ0YwK2cwTGJUUERmRHkiLCJtYWMiOiIyZDM4YmIwNTA3YjQ3MGI1MmVjMWI0NWM4NGU2OTYwZGMxOTI4NWQxNDhhNTdlOWIyMWVlMjEzZGMzN2Q3ZTllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-237296111\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2129371995 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129371995\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-77035586 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:27:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhyMDRTZnFRdHJvaThEMGkrUGt2dGc9PSIsInZhbHVlIjoibUZXSUtwVllFcFRKOHo0ZjNhekRBajdrN2hyY0hyMnZESTBYWEFXOTdOZmE2MVF2ZURqcWtNUWExOVNERHJ0TXh0VytKVjlDN1NWSXlWT0RSQnhSck05YUY2b0c5bFJxeEtwY1Iwd2Jvckc1UFNMQWVjK2pWSU5CQjhMSW83Tm8iLCJtYWMiOiIxMDZhYjgzNjI1NTZiNTNmZDE1NmVkNGZmZDUyZTgxNzgwM2M1OGQ2N2E2NDNmMjFiODhiYWZjNDY4NmMxMTBlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ijg5K3lRZTFvc3ZscDNnMFZpWWVPSmc9PSIsInZhbHVlIjoiUjhXUmtDUUlmTEdJb2tJcFJHd1NrY1UxN1ZmbFMyMk5hM3RRUHcxcEZMS2lTc2YvN3BKTUc5R1JNNllBa2Y4K3JvZ2szZmExOWlaRzd5VFdCM1dRZEUvSGIycThrQld0U3R0VFd3SHVnYjlHbDJzWHYvM3Y2U0NQejIyUFYrWWwiLCJtYWMiOiIyN2I5YmYxZDlmZDA5YTc1YjAxMmE3N2M3ZTJlNThiMDQ4YmNmNTk2NjNiYzRkZGM0NjI5NjQ1ODEwOTJhYjZiIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhyMDRTZnFRdHJvaThEMGkrUGt2dGc9PSIsInZhbHVlIjoibUZXSUtwVllFcFRKOHo0ZjNhekRBajdrN2hyY0hyMnZESTBYWEFXOTdOZmE2MVF2ZURqcWtNUWExOVNERHJ0TXh0VytKVjlDN1NWSXlWT0RSQnhSck05YUY2b0c5bFJxeEtwY1Iwd2Jvckc1UFNMQWVjK2pWSU5CQjhMSW83Tm8iLCJtYWMiOiIxMDZhYjgzNjI1NTZiNTNmZDE1NmVkNGZmZDUyZTgxNzgwM2M1OGQ2N2E2NDNmMjFiODhiYWZjNDY4NmMxMTBlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ijg5K3lRZTFvc3ZscDNnMFZpWWVPSmc9PSIsInZhbHVlIjoiUjhXUmtDUUlmTEdJb2tJcFJHd1NrY1UxN1ZmbFMyMk5hM3RRUHcxcEZMS2lTc2YvN3BKTUc5R1JNNllBa2Y4K3JvZ2szZmExOWlaRzd5VFdCM1dRZEUvSGIycThrQld0U3R0VFd3SHVnYjlHbDJzWHYvM3Y2U0NQejIyUFYrWWwiLCJtYWMiOiIyN2I5YmYxZDlmZDA5YTc1YjAxMmE3N2M3ZTJlNThiMDQ4YmNmNTk2NjNiYzRkZGM0NjI5NjQ1ODEwOTJhYjZiIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77035586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-132452639 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-132452639\", {\"maxDepth\":0})</script>\n"}}