{"__meta": {"id": "Xbc7ad302d6e5ebe4d9b107f26894fddd", "datetime": "2025-07-20 08:41:38", "utime": 1752975698.848143, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.114082, "end": 1752975698.848167, "duration": 1.7340848445892334, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": **********.114082, "relative_start": 0, "end": **********.406673, "relative_end": **********.406673, "duration": 0.292590856552124, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.406689, "relative_start": 0.29260683059692383, "end": 1752975698.84817, "relative_end": 3.0994415283203125e-06, "duration": 1.441481113433838, "duration_str": "1.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 28971936, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "dashboard", "param_count": null, "params": [], "start": 1752975698.387006, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dashboard.blade.phpdashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "dashboard.notification-items", "param_count": null, "params": [], "start": 1752975698.832191, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dashboard/notification-items.blade.phpdashboard.notification-items", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdashboard%2Fnotification-items.blade.php&line=1", "ajax": false, "filename": "notification-items.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": 1752975698.840775, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": 1752975698.841966, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": 1752975698.843305, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dashboard", "middleware": "web, auth, admin.kapal", "controller": "App\\Http\\Controllers\\DashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=15\" onclick=\"\">app/Http/Controllers/DashboardController.php:15-180</a>"}, "queries": {"nb_statements": 398, "nb_visible_statements": 399, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10229000000000008, "accumulated_duration_str": "102ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 298 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.467363, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.472879, "duration": 0.01648, "duration_str": "16.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 16.111}, {"sql": "select * from `kapal` where `jenis_kapal_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 17}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.498287, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:17", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=17", "ajax": false, "filename": "DashboardController.php", "line": "17"}, "connection": "gema_kapal", "explain": null, "start_percent": 16.111, "width_percent": 0.635}, {"sql": "select * from `kapal` where `jenis_kapal_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 18}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.503052, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:18", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=18", "ajax": false, "filename": "DashboardController.php", "line": "18"}, "connection": "gema_kapal", "explain": null, "start_percent": 16.747, "width_percent": 0.557}, {"sql": "select count(*) as aggregate from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 4 and `dokumen_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.508239, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:35", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=35", "ajax": false, "filename": "DashboardController.php", "line": "35"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.304, "width_percent": 0.567}, {"sql": "select * from `checklist_data_engine` order by `waktu` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5104122, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:24", "source": {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=24", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.871, "width_percent": 0.616}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.514997, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.487, "width_percent": 0.44}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 42 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 42, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5167358, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.927, "width_percent": 0.753}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.518493, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.679, "width_percent": 0.225}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.51998, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.904, "width_percent": 0.342}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.521359, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.246, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 44 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 44, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.522336, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.364, "width_percent": 0.323}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.523572, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.686, "width_percent": 0.186}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.524736, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.872, "width_percent": 0.362}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5261319, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.234, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 54 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 54, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.52751, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.459, "width_percent": 0.489}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.529017, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.947, "width_percent": 0.381}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.530401, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.329, "width_percent": 0.323}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 58 limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.531745, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.651, "width_percent": 0.166}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 58 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 58, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.532808, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.817, "width_percent": 0.303}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5341408, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.121, "width_percent": 0.401}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.535626, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.521, "width_percent": 0.323}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5369651, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.844, "width_percent": 0.127}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 65 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 65, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.537948, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.971, "width_percent": 0.362}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.539232, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.333, "width_percent": 0.166}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.54035, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.499, "width_percent": 0.391}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.54174, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.89, "width_percent": 0.156}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 72 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 72, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.542765, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.046, "width_percent": 0.391}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.544081, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.437, "width_percent": 0.137}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.545155, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.574, "width_percent": 0.274}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 87 limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.546382, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.848, "width_percent": 0.137}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 87 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 87, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.547446, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.985, "width_percent": 0.577}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.548993, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.562, "width_percent": 0.137}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.550045, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.699, "width_percent": 0.342}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 88 limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5513391, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.041, "width_percent": 0.147}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 88 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 88, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.552358, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.187, "width_percent": 0.362}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.553632, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.549, "width_percent": 0.166}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.554825, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.715, "width_percent": 0.528}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.556356, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.243, "width_percent": 0.176}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 94 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 94, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.557384, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.419, "width_percent": 0.323}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.558644, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.742, "width_percent": 0.371}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.560066, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.113, "width_percent": 0.323}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.561449, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.436, "width_percent": 0.45}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 96 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 96, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.564374, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.886, "width_percent": 0.528}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.565939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.414, "width_percent": 0.352}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5672898, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.765, "width_percent": 0.215}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.568568, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.981, "width_percent": 0.391}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 99 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 99, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.570055, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.372, "width_percent": 0.391}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.571464, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.763, "width_percent": 0.303}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.572742, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.066, "width_percent": 0.205}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 106 limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5739338, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.271, "width_percent": 0.205}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 106 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 106, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.575137, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.476, "width_percent": 0.391}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.576601, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.867, "width_percent": 0.323}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.577877, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.19, "width_percent": 0.215}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.579113, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.405, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 109 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 109, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.580242, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.62, "width_percent": 0.244}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.581393, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.865, "width_percent": 0.284}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.582677, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.148, "width_percent": 0.186}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.583842, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.334, "width_percent": 0.205}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 112 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 112, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.584895, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.539, "width_percent": 0.235}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5860791, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.774, "width_percent": 0.274}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 118 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 118, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.587219, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.047, "width_percent": 0.186}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.588364, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.233, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 120 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 120, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.58956, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.448, "width_percent": 0.313}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5909488, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.761, "width_percent": 0.254}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 123 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 123, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5920992, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.015, "width_percent": 0.264}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.593351, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.279, "width_percent": 0.254}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 131 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 131, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5944848, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.533, "width_percent": 0.244}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 134 limit 1", "type": "query", "params": [], "bindings": [134], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.59572, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.778, "width_percent": 0.459}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 134 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 134, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.597347, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.237, "width_percent": 0.45}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 138 limit 1", "type": "query", "params": [], "bindings": [138], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.598838, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.687, "width_percent": 0.352}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 138 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 138, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6001608, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.039, "width_percent": 0.254}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.601403, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.293, "width_percent": 0.264}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 139 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 139, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.602619, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.557, "width_percent": 0.381}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 140 limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.604168, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.938, "width_percent": 0.342}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 140 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 140, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6054668, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.28, "width_percent": 0.244}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 141 limit 1", "type": "query", "params": [], "bindings": [141], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.606701, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.525, "width_percent": 0.205}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 141 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 141, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.607778, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.73, "width_percent": 0.616}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6094458, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.346, "width_percent": 0.235}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 143 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 143, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.610687, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.581, "width_percent": 0.284}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.61192, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.864, "width_percent": 0.303}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6132078, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.167, "width_percent": 0.225}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.614421, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.392, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 18 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 18, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6154919, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.607, "width_percent": 0.235}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.616647, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.842, "width_percent": 0.284}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 22 limit 1", "type": "query", "params": [], "bindings": [4, 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.618012, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.125, "width_percent": 0.518}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 50 limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6211991, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.643, "width_percent": 0.323}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 50 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 50, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.622428, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.966, "width_percent": 0.254}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.623641, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.22, "width_percent": 0.332}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.624968, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.553, "width_percent": 0.196}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.626153, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.748, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 52 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 52, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.627272, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.963, "width_percent": 0.254}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6284308, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.217, "width_percent": 0.244}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6296098, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.462, "width_percent": 0.254}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.631424, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.716, "width_percent": 0.587}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 93 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 93, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6330419, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.303, "width_percent": 0.362}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.634399, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.664, "width_percent": 0.196}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6355212, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.86, "width_percent": 0.264}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 117 limit 1", "type": "query", "params": [], "bindings": [117], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.636759, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.124, "width_percent": 0.147}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 117 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 117, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.637825, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.27, "width_percent": 0.557}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 145 limit 1", "type": "query", "params": [], "bindings": [145], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DashboardController.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.639493, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.828, "width_percent": 0.391}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.640869, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.219, "width_percent": 0.371}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641345, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.59, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.641569, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.688, "width_percent": 0.42}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.642059, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.108, "width_percent": 0.127}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.642253, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.235, "width_percent": 0.147}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6424892, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.382, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6426911, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.47, "width_percent": 0.459}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.643213, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.93, "width_percent": 0.127}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.643393, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.057, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6436121, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.193, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.643824, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.291, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6440492, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.467, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6442208, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.565, "width_percent": 0.254}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6446018, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.819, "width_percent": 0.244}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644995, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.064, "width_percent": 0.538}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6455932, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.601, "width_percent": 0.127}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6457782, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.728, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64599, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.855, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646171, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.934, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646389, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.11, "width_percent": 0.244}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6466892, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.354, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.646896, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.481, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6470761, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.559, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6473048, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.716, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.647475, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.794, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6477032, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.95, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.647943, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.038, "width_percent": 0.557}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.648751, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.596, "width_percent": 0.196}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649078, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.791, "width_percent": 0.205}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649347, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.996, "width_percent": 0.274}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649704, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.27, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649935, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.407, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650131, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.495, "width_percent": 0.166}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6503682, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.661, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65056, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.749, "width_percent": 0.166}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6507962, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.915, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651001, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.003, "width_percent": 0.293}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6513681, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.297, "width_percent": 0.479}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651929, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.776, "width_percent": 0.196}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.652219, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.971, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6524339, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.069, "width_percent": 0.186}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6526651, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.255, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6528301, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.362, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65303, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.48, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653211, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.558, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653422, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.724, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653568, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.822, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653761, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.939, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653938, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.017, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65415, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.183, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654299, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.281, "width_percent": 0.186}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6545622, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.467, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.654752, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.555, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6549878, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.711, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655181, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.79, "width_percent": 0.489}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655781, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.278, "width_percent": 0.166}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656074, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.445, "width_percent": 0.215}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656347, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.66, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6565092, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.767, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656727, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.904, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656923, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.992, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6571481, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.168, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6573, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.266, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657498, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.383, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657686, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.461, "width_percent": 0.362}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658136, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.823, "width_percent": 0.244}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658469, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.067, "width_percent": 0.362}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6589391, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.429, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659163, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.527, "width_percent": 0.293}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6595252, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.82, "width_percent": 0.127}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6597161, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.947, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6599221, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.074, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660119, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.162, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66033, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.328, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660478, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.426, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660683, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.553, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660867, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.632, "width_percent": 0.313}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661226, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.944, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6613882, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.052, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66158, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.169, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66179, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.267, "width_percent": 0.577}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662449, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.844, "width_percent": 0.186}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662703, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.03, "width_percent": 0.147}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662944, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.176, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663142, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.264, "width_percent": 0.176}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6633961, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.44, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663583, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.528, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663825, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.685, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664233, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.909, "width_percent": 0.342}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.664816, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.252, "width_percent": 0.156}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6651149, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.408, "width_percent": 0.714}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665926, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.122, "width_percent": 0.518}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6665509, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.64, "width_percent": 0.401}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66708, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.041, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6674502, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.265, "width_percent": 0.274}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6677861, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.539, "width_percent": 0.127}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6679702, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.666, "width_percent": 0.284}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668359, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.95, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668554, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.038, "width_percent": 0.186}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668787, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.223, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668966, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.331, "width_percent": 0.205}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6692832, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.536, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.669565, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.654, "width_percent": 0.723}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6703591, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.377, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67052, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.485, "width_percent": 0.147}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6707518, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.631, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671089, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.856, "width_percent": 0.186}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6713562, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.042, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671563, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.13, "width_percent": 0.352}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672028, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.482, "width_percent": 0.108}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672278, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.589, "width_percent": 0.508}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672936, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.098, "width_percent": 0.381}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673486, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.479, "width_percent": 0.235}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.673813, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.714, "width_percent": 0.274}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674211, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.987, "width_percent": 0.166}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6744502, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.153, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67465, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.241, "width_percent": 0.196}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674905, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.437, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675103, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.545, "width_percent": 0.323}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6755352, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.867, "width_percent": 0.108}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675755, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.975, "width_percent": 0.196}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676003, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.17, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676177, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.278, "width_percent": 0.313}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676598, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.591, "width_percent": 0.108}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67682, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.698, "width_percent": 0.196}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6770651, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.894, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677238, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.011, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677442, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.138, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677639, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.226, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.677865, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.402, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678018, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.5, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6782289, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.617, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678588, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.842, "width_percent": 0.215}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678857, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.057, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679039, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.174, "width_percent": 0.147}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679304, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.321, "width_percent": 0.215}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6796598, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.536, "width_percent": 0.244}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679963, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.78, "width_percent": 0.274}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6803071, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.054, "width_percent": 0.147}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68054, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.201, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68073, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.289, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6809552, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.465, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681116, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.572, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6813269, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.699, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681523, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.778, "width_percent": 0.342}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68193, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.12, "width_percent": 0.137}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682127, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.257, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682351, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.393, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682553, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.481, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68278, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.657, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682945, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.765, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6831632, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.892, "width_percent": 0.264}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683564, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.156, "width_percent": 0.254}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6838732, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.41, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684045, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.528, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6842651, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.664, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684472, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.762, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6847448, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.938, "width_percent": 0.235}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6851568, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.173, "width_percent": 0.244}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6855052, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.417, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685719, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.515, "width_percent": 0.205}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685987, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.72, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686194, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.838, "width_percent": 0.499}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.68681, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.336, "width_percent": 0.147}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687077, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.483, "width_percent": 0.205}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6873322, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.688, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687505, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.805, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687767, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.942, "width_percent": 0.235}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688278, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.177, "width_percent": 0.313}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6886492, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.49, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688798, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.587, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689007, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.715, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6892, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.803, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6894171, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.969, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689584, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.066, "width_percent": 0.313}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.690004, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.379, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6902359, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.497, "width_percent": 0.196}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.690485, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.692, "width_percent": 0.332}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.690881, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.025, "width_percent": 0.176}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691161, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.201, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6915278, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.425, "width_percent": 0.225}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691807, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.65, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691988, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.768, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6922271, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.904, "width_percent": 0.284}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692659, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.188, "width_percent": 0.205}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692975, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.393, "width_percent": 0.264}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693395, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.657, "width_percent": 0.254}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693753, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.911, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693987, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.029, "width_percent": 0.166}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6942408, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.195, "width_percent": 0.225}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.694572, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.42, "width_percent": 0.166}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6948168, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.586, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.694995, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.664, "width_percent": 0.156}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695198, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.821, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695346, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.918, "width_percent": 0.137}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695565, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.055, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695746, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.133, "width_percent": 0.156}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6959758, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.29, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6961598, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.368, "width_percent": 0.391}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69667, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.759, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69691, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.876, "width_percent": 0.196}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6972072, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.072, "width_percent": 0.098}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6974251, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.17, "width_percent": 0.313}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697851, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.482, "width_percent": 0.235}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69821, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.717, "width_percent": 0.186}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6984482, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.903, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698611, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.01, "width_percent": 0.127}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698817, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.137, "width_percent": 0.088}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699011, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.225, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6992261, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.392, "width_percent": 0.098}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699381, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.489, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69958, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.607, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699754, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.685, "width_percent": 0.176}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699998, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.861, "width_percent": 0.342}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700448, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.203, "width_percent": 0.264}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.700844, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.467, "width_percent": 0.313}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701309, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.78, "width_percent": 0.254}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701623, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.034, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701811, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.151, "width_percent": 0.196}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702138, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.347, "width_percent": 0.137}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7023969, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.484, "width_percent": 0.186}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7026331, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.669, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.702791, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.777, "width_percent": 0.127}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.703, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.904, "width_percent": 0.137}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.703185, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.041, "width_percent": 0.127}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7033641, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.168, "width_percent": 0.117}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.703554, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.285, "width_percent": 0.078}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.70373, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.364, "width_percent": 0.166}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.703938, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.53, "width_percent": 0.244}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704252, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.774, "width_percent": 0.411}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704765, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.185, "width_percent": 0.166}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705042, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.351, "width_percent": 0.205}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7052982, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.556, "width_percent": 0.108}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705456, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.664, "width_percent": 0.137}, {"sql": "select * from `checklist_jangkar` where `checklist_jangkar`.`kapal_id` = ? and `checklist_jangkar`.`kapal_id` is not null order by `tanggal` desc, `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706151, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.801, "width_percent": 0.274}, {"sql": "select * from `checklist_engines` where `checklist_engines`.`kapal_id` = ? and `checklist_engines`.`kapal_id` is not null order by `tanggal` desc, `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707165, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.074, "width_percent": 0.381}, {"sql": "select count(*) as aggregate from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = ? and `dokumen_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707743, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.456, "width_percent": 0.411}, {"sql": "select * from `checklist_jangkar` where `checklist_jangkar`.`kapal_id` = ? and `checklist_jangkar`.`kapal_id` is not null order by `tanggal` desc, `created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708279, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.866, "width_percent": 0.371}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3600092, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.238, "width_percent": 0.616}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.360935, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.854, "width_percent": 0.459}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.36156, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.313, "width_percent": 0.274}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3619862, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.587, "width_percent": 0.137}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.362209, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.724, "width_percent": 0.117}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.362415, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.841, "width_percent": 0.108}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.362596, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.949, "width_percent": 0.098}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3627741, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.046, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3629272, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.134, "width_percent": 0.098}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.363101, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.232, "width_percent": 0.166}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.363337, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.398, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3635032, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.486, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3636508, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.574, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3637938, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.652, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.36393, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.731, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3640728, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.809, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.364223, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.887, "width_percent": 0.166}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3645031, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.053, "width_percent": 0.117}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.364695, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.171, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3648608, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.259, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.365, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.337, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.36515, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.415, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.365284, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.493, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.365425, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.571, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.36558, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.659, "width_percent": 0.196}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.365868, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.855, "width_percent": 0.117}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3660562, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.972, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.366213, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.06, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3663619, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.148, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.366511, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.226, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.366664, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.314, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.366813, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.393, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.366961, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.471, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.367129, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.559, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.367303, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.647, "width_percent": 0.254}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3676882, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.901, "width_percent": 0.117}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3678741, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.018, "width_percent": 0.098}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3680549, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.116, "width_percent": 0.186}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.368323, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.302, "width_percent": 0.098}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3684921, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.4, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.368639, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.488, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.36879, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.566, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3689308, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.644, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.369078, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.722, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.369216, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.8, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3693578, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.879, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.369495, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.957, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.369639, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.035, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.369773, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.113, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3699179, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.191, "width_percent": 0.078}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.370068, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.27, "width_percent": 0.362}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.370544, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.631, "width_percent": 0.293}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.370949, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.925, "width_percent": 0.352}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3714051, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.277, "width_percent": 0.166}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.371648, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.443, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.37183, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.531, "width_percent": 0.108}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3720312, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.638, "width_percent": 0.098}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3722012, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.736, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3723502, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.824, "width_percent": 0.088}, {"sql": "select * from `kapal` where `nama` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1752975698.3725052, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.912, "width_percent": 0.088}]}, "models": {"data": {"App\\Models\\ChecklistDataEngine": {"value": 273, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=1", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ChecklistJangkar": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistJangkar.php&line=1", "ajax": false, "filename": "ChecklistJangkar.php", "line": "?"}}}, "count": 277, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dashboard\"\n]"}, "request": {"path_info": "/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1670277474 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1670277474\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2115097953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2115097953\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-598914851 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-598914851\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-861075837 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InVYOTFOV0pLWk1aVlB2dzRsNDNIMlE9PSIsInZhbHVlIjoidy85RUU5YjVqRjVPa0RlRTVhUjlzQWtlMjhuWmlyS3Rpckx3ZmhrWmcxQWNsYlFHUFRNTUVkeHZ4TGFnZE5JV1ZLYTNkNlhTVnNxcTFRcnVvcVZNTmNoNDVkZEV4aS9GMmFxaEJIbFNQZEwrRXlYdjFKRVcxT0J6dHlhMExFbnUiLCJtYWMiOiI3ZDA5Yzg5YWQxNWUwZDAyNWYwYjUwNjA2Y2JjOWZlY2RiNjJjZDZjYTVlMDZjZTgwYmJiMTc3ZGQ4NTE1MDM2IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6InVKTTJuSmtWSmdDZTEyZHZleDJIdVE9PSIsInZhbHVlIjoicWJtUUlxUm80ajJUYUNIOW9icjVpT1Qxc3V2QnRDVDdxTmNUNENQSEEzaUtvSUJwY0h1Y01nalJhMkJIeEttOWpyQkVRK0NCWjNDQ3p5bVA2aHVIKzNSZUJLWHVuNWM4SjNDOFl0NFJsUjVodU42MGZmM1hyaTRYR2lzcU1sT24iLCJtYWMiOiIyZGFlMzE4MTYzMjk5ODNjYzM3NDk2NDZhNDliNWE5OTc0NjM2ZGRmYmIyN2MzYzNkNmIyMjI5MGY3NTBlZTNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861075837\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-124975850 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z47ckW1sW3vByhJlsabzz4Jt9SLwE6fzIeTpobGt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124975850\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-910193699 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:41:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhRb0ZRM3IwczhpUnkyN0k0Tmx0OUE9PSIsInZhbHVlIjoiWUg0ak5jY1RHYlNPUlZZMUUwcDhJVEd4U3RycDN2ZHBYT0R3SlF6eGQvWXZ5MDN3RzRORjJ3aVY4V2dORmZCRlhFZXhVZEE5Yk5pSi9UM3JvZ2M0MjhCVWZ4TWZUTVZxeW1ycUk5ZDd6KzVPMlh6QXpqRFFibStPTmhFQmU1eVEiLCJtYWMiOiJjNGEyZDM2NDU2YWRjOWM4YjY2MmJlNzA0M2MxNzE0YjI0Y2M2NmJiYzc4YjJlZjg2YjYxNmM2OTJkOTY2ZDllIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:41:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IkxvSTVBWGZDY20rbkpEYkNYcHRJUlE9PSIsInZhbHVlIjoiR2hYVVRjTUNOS293R2syRExMeWw0YXBoQWNlMmdkTVR2TVBwVlllUlo0QnFzblFKeldLYnJhYzlxTEN5aVVZUW1ReTJxOCtscUJRR3A4bUFjSjBpSEZndWM3N3BHWGFTNGl3VERaWWNvbTVnQlRhMkpKWUdrK0ZFVDYrT0lFcFciLCJtYWMiOiIwN2U3NDhlOWVkMmE1ZmY3ZmEwNmNmZWYxZmRjMzkzY2Y2ZDA0YTA2NGNiNGU5YzA2ZWE3YjgyYmU0ZjNkMWM5IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:41:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhRb0ZRM3IwczhpUnkyN0k0Tmx0OUE9PSIsInZhbHVlIjoiWUg0ak5jY1RHYlNPUlZZMUUwcDhJVEd4U3RycDN2ZHBYT0R3SlF6eGQvWXZ5MDN3RzRORjJ3aVY4V2dORmZCRlhFZXhVZEE5Yk5pSi9UM3JvZ2M0MjhCVWZ4TWZUTVZxeW1ycUk5ZDd6KzVPMlh6QXpqRFFibStPTmhFQmU1eVEiLCJtYWMiOiJjNGEyZDM2NDU2YWRjOWM4YjY2MmJlNzA0M2MxNzE0YjI0Y2M2NmJiYzc4YjJlZjg2YjYxNmM2OTJkOTY2ZDllIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:41:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IkxvSTVBWGZDY20rbkpEYkNYcHRJUlE9PSIsInZhbHVlIjoiR2hYVVRjTUNOS293R2syRExMeWw0YXBoQWNlMmdkTVR2TVBwVlllUlo0QnFzblFKeldLYnJhYzlxTEN5aVVZUW1ReTJxOCtscUJRR3A4bUFjSjBpSEZndWM3N3BHWGFTNGl3VERaWWNvbTVnQlRhMkpKWUdrK0ZFVDYrT0lFcFciLCJtYWMiOiIwN2U3NDhlOWVkMmE1ZmY3ZmEwNmNmZWYxZmRjMzkzY2Y2ZDA0YTA2NGNiNGU5YzA2ZWE3YjgyYmU0ZjNkMWM5IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:41:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910193699\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2064606127 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064606127\", {\"maxDepth\":0})</script>\n"}}