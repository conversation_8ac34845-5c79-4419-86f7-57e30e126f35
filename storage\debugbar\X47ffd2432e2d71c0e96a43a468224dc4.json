{"__meta": {"id": "X47ffd2432e2d71c0e96a43a468224dc4", "datetime": "2025-07-20 08:09:07", "utime": **********.837869, "method": "GET", "uri": "/checklist-engine/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[08:09:07] LOG.info: Kapal: {\n    \"id\": 4,\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.53582, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.282453, "end": **********.837894, "duration": 0.5554409027099609, "duration_str": "555ms", "measures": [{"label": "Booting", "start": **********.282453, "relative_start": 0, "end": **********.470838, "relative_end": **********.470838, "duration": 0.188385009765625, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.470852, "relative_start": 0.1883988380432129, "end": **********.837897, "relative_end": 3.0994415283203125e-06, "duration": 0.36704516410827637, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33356336, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "checklist-engine.index", "param_count": null, "params": [], "start": **********.788078, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/checklist-engine/index.blade.phpchecklist-engine.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fchecklist-engine%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.795797, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.816582, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.818539, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.819793, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.830473, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET checklist-engine/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ChecklistEngineController@index", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=19\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:19-133</a>"}, "queries": {"nb_statements": 344, "nb_visible_statements": 345, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10419999999999999, "accumulated_duration_str": "104ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 244 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.511144, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.51549, "duration": 0.01493, "duration_str": "14.93ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 14.328}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5385618, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:34", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=34", "ajax": false, "filename": "ChecklistEngineController.php", "line": "34"}, "connection": "gema_kapal", "explain": null, "start_percent": 14.328, "width_percent": 0.557}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5404449, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:47", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=47", "ajax": false, "filename": "ChecklistEngineController.php", "line": "47"}, "connection": "gema_kapal", "explain": null, "start_percent": 14.885, "width_percent": 0.557}, {"sql": "select `hpe`.*, `cde`.`item_pemeriksaan` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`history_id` = 24", "type": "query", "params": [], "bindings": [4, 24], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5425909, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:55", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=55", "ajax": false, "filename": "ChecklistEngineController.php", "line": "55"}, "connection": "gema_kapal", "explain": null, "start_percent": 15.441, "width_percent": 0.528}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.547312, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 15.969, "width_percent": 0.816}, {"sql": "select `hpe`.*, `cde`.`item_pemeri<PERSON><PERSON>`, `hpk`.`tujuan`, `hpk`.`rh_me_ps`, `hpk`.`rh_me_sb` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON>n_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 order by `hpe`.`tanggal_pemeriksaan` desc, `hpe`.`created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.549434, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 16.785, "width_percent": 0.509}, {"sql": "select count(*) as aggregate from `checklist_engines` where `kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5519152, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:99", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=99", "ajax": false, "filename": "ChecklistEngineController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.294, "width_percent": 0.441}, {"sql": "select * from `checklist_data_engine` order by `waktu` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.5539632, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:24", "source": {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=24", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.735, "width_percent": 0.595}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5633018, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.33, "width_percent": 0.585}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 42 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 42, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5656629, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.916, "width_percent": 0.547}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.567417, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.463, "width_percent": 0.202}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.569155, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.664, "width_percent": 0.441}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.570769, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.106, "width_percent": 0.163}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 44 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 44, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5719469, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.269, "width_percent": 0.374}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.573401, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.643, "width_percent": 0.173}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.574654, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.816, "width_percent": 0.269}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.576269, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.084, "width_percent": 0.489}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 54 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 54, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.577862, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.574, "width_percent": 0.537}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.579574, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.111, "width_percent": 0.317}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.581045, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.428, "width_percent": 0.653}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 58 limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.583014, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.081, "width_percent": 0.393}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 58 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 58, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5844781, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.474, "width_percent": 0.633}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.586285, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.107, "width_percent": 0.326}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.58781, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.434, "width_percent": 0.374}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.589603, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.808, "width_percent": 0.317}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 65 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 65, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5910912, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.125, "width_percent": 0.47}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.592763, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.595, "width_percent": 0.365}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.594402, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.96, "width_percent": 0.662}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.59635, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.622, "width_percent": 0.182}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 72 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 72, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.597658, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.804, "width_percent": 0.585}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5994248, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.39, "width_percent": 0.211}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.600846, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.601, "width_percent": 0.633}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 87 limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.602764, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.234, "width_percent": 0.393}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 87 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 87, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6043441, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.628, "width_percent": 0.835}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.606471, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.463, "width_percent": 0.47}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.608088, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.933, "width_percent": 0.787}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 88 limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.610024, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.72, "width_percent": 0.47}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 88 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 88, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.611682, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.19, "width_percent": 0.739}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.613617, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.929, "width_percent": 0.739}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6154962, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.668, "width_percent": 0.47}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6171901, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.138, "width_percent": 0.441}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 94 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 94, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6188412, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.58, "width_percent": 0.432}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.620368, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.012, "width_percent": 0.24}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6217709, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.251, "width_percent": 0.365}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6233, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.616, "width_percent": 0.144}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 96 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 96, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6245072, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.76, "width_percent": 0.787}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.626461, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.547, "width_percent": 0.221}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6277509, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.768, "width_percent": 0.345}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.629248, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.113, "width_percent": 0.163}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 99 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 99, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6304162, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.276, "width_percent": 0.298}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6318948, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.574, "width_percent": 0.25}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6332688, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.823, "width_percent": 0.403}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 106 limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.634776, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.226, "width_percent": 0.154}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 106 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 106, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.635885, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.38, "width_percent": 0.422}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6373801, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.802, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6386712, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.927, "width_percent": 0.461}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.640351, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.388, "width_percent": 0.163}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 109 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 109, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6415012, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.551, "width_percent": 0.441}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.643008, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.992, "width_percent": 0.173}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.644221, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.165, "width_percent": 0.317}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6457021, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.482, "width_percent": 0.326}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 112 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 112, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6471062, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.808, "width_percent": 0.441}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.648724, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.25, "width_percent": 0.307}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 118 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 118, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.650098, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.557, "width_percent": 0.643}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6521628, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.2, "width_percent": 0.413}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 120 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 120, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.653691, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.612, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.655313, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.044, "width_percent": 0.278}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 123 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 123, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.656666, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.322, "width_percent": 0.48}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.658312, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.802, "width_percent": 0.355}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 131 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 131, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.659847, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.157, "width_percent": 0.451}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 134 limit 1", "type": "query", "params": [], "bindings": [134], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6615129, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.608, "width_percent": 0.393}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 134 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 134, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.662952, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.002, "width_percent": 0.528}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 138 limit 1", "type": "query", "params": [], "bindings": [138], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.664711, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.53, "width_percent": 0.317}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 138 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 138, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.666446, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 44.846, "width_percent": 0.48}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.668121, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.326, "width_percent": 0.307}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 139 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 139, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.669484, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.633, "width_percent": 0.336}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 140 limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.670984, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 45.969, "width_percent": 0.278}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 140 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 140, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.672329, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.248, "width_percent": 0.489}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 141 limit 1", "type": "query", "params": [], "bindings": [141], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.674111, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 46.737, "width_percent": 0.365}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 141 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 141, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.675572, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.102, "width_percent": 0.489}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6773329, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.591, "width_percent": 0.403}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 143 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 143, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.678801, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 47.994, "width_percent": 0.509}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.680803, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.503, "width_percent": 0.355}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.682272, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 48.858, "width_percent": 0.384}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.68375, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.242, "width_percent": 0.154}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 18 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 18, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6848679, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.395, "width_percent": 0.393}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6863198, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 49.789, "width_percent": 0.384}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 22 limit 1", "type": "query", "params": [], "bindings": [4, 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6879652, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.173, "width_percent": 0.48}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 50 limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.689624, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.653, "width_percent": 0.144}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 50 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 50, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.690737, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.797, "width_percent": 0.365}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.692185, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.161, "width_percent": 0.154}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.693389, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.315, "width_percent": 0.547}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.695753, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 51.862, "width_percent": 0.47}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 52 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 52, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.697307, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.332, "width_percent": 0.278}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.69869, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.61, "width_percent": 0.307}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7000399, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 52.917, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.701535, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.148, "width_percent": 0.48}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 93 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 93, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7031379, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.628, "width_percent": 0.355}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.704578, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 53.983, "width_percent": 0.509}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.706205, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 54.491, "width_percent": 0.23}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.707564, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.722, "width_percent": 0.47}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708214, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.192, "width_percent": 0.259}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708599, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.451, "width_percent": 0.134}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.708863, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.585, "width_percent": 0.192}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7091522, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.777, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709346, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.864, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709572, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.036, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709751, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.142, "width_percent": 0.211}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7100692, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.353, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7103229, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.468, "width_percent": 0.211}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710593, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.679, "width_percent": 0.202}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710851, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.881, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711075, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.015, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711269, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.102, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711492, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.274, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711643, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.37, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.711833, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.486, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712023, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.572, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71224, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.745, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712399, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.85, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7125938, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.965, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712778, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.042, "width_percent": 0.154}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.712979, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.196, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7131312, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.292, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.713327, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.407, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7135062, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.484, "width_percent": 0.259}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7138438, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.743, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714037, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.82, "width_percent": 0.298}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7144618, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.117, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.714854, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.347, "width_percent": 0.47}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.715475, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.818, "width_percent": 0.154}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7157738, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.971, "width_percent": 0.24}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7160761, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.211, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71626, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.336, "width_percent": 0.144}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7164922, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.48, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7167, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.566, "width_percent": 0.355}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717158, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.921, "width_percent": 0.106}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.717384, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.027, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7176292, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.19, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7178261, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.276, "width_percent": 0.278}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718171, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.555, "width_percent": 0.393}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718654, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.948, "width_percent": 0.211}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.718955, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.159, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719169, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.255, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719398, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.438, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.719569, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.553, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7197702, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.678, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.71996, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.764, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7201982, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.937, "width_percent": 0.259}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.720537, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.196, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7207842, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.349, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7210019, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.445, "width_percent": 0.211}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7212868, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.656, "width_percent": 0.25}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.721636, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.906, "width_percent": 0.336}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722115, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.242, "width_percent": 0.144}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722401, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.386, "width_percent": 0.336}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.722874, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.722, "width_percent": 0.221}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.723244, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.942, "width_percent": 0.374}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7237222, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.317, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.723922, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.403, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7241342, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.566, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7242951, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.672, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7245069, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.797, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.724688, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.873, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7249, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.036, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7250571, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.132, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.725603, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.564, "width_percent": 0.163}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72591, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.727, "width_percent": 0.374}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726379, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.102, "width_percent": 0.154}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7266161, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.255, "width_percent": 0.259}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.726969, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.514, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7271829, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.61, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727421, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.793, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7275841, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.898, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727785, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.023, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.727986, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.109, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7282572, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.292, "width_percent": 0.288}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7286549, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.58, "width_percent": 0.221}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.728996, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.8, "width_percent": 0.307}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729462, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.107, "width_percent": 0.25}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729767, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.357, "width_percent": 0.134}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.729971, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.491, "width_percent": 0.221}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.730295, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.712, "width_percent": 0.173}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.730603, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.885, "width_percent": 0.221}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7308822, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.106, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7310421, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.211, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7312648, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.336, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.731634, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.566, "width_percent": 0.25}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.731981, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.816, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732188, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.912, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7324371, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.075, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732626, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.161, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.732859, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.315, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733042, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.392, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7332692, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.555, "width_percent": 0.259}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733614, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.814, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7338681, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.967, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7341158, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.083, "width_percent": 0.403}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7346191, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.486, "width_percent": 0.134}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.734823, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.62, "width_percent": 0.144}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7350721, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.764, "width_percent": 0.298}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.735536, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.061, "width_percent": 0.278}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.735893, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.34, "width_percent": 0.365}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736339, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.704, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736594, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.868, "width_percent": 0.202}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736907, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.069, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7371361, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.251, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7372952, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.357, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7375178, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.491, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.737698, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.568, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7379448, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.731, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738116, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.808, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738353, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.962, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.73854, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.048, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738772, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.202, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738951, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.278, "width_percent": 0.144}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739163, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.422, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739336, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.499, "width_percent": 0.144}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739543, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.643, "width_percent": 0.192}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739841, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.835, "width_percent": 0.154}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.740044, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.988, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74019, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.084, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.740398, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.209, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.740575, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.286, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7407882, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.449, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7409382, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.545, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741122, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.66, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741301, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.747, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741517, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.919, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741681, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.025, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741899, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.14, "width_percent": 0.288}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742343, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.428, "width_percent": 0.537}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.742993, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.965, "width_percent": 0.278}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7433708, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.244, "width_percent": 0.25}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7437708, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.493, "width_percent": 0.221}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74413, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.714, "width_percent": 0.202}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7443912, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.916, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7445571, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.021, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7447662, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.146, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744991, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.232, "width_percent": 0.518}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745577, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.75, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745727, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.846, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.745925, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.962, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746102, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.038, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7463412, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.211, "width_percent": 0.259}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746684, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.47, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7469301, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.624, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747134, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.72, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747365, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.902, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7475312, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.008, "width_percent": 0.115}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7477298, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.123, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747911, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.2, "width_percent": 0.163}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748125, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.363, "width_percent": 0.096}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748291, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.459, "width_percent": 0.403}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7488291, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.862, "width_percent": 0.307}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749306, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.169, "width_percent": 0.48}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749902, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.649, "width_percent": 0.288}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7502968, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.937, "width_percent": 0.221}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750628, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.157, "width_percent": 0.125}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750874, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.282, "width_percent": 0.192}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7511241, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.474, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751287, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.58, "width_percent": 0.413}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.751799, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.992, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75202, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.107, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.752242, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.28, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7524002, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.386, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.752613, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.52, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7528, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.607, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7530231, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.779, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753181, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.885, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753402, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.01, "width_percent": 0.24}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.753773, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.25, "width_percent": 0.211}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754046, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.461, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7542322, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.576, "width_percent": 0.461}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7548041, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.036, "width_percent": 0.115}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755038, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.152, "width_percent": 0.192}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7552872, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.344, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755445, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.449, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755661, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.583, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7558732, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.67, "width_percent": 0.259}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756216, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.929, "width_percent": 0.154}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7564619, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.083, "width_percent": 0.403}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7570071, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.486, "width_percent": 0.278}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7574708, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.764, "width_percent": 0.23}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757774, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.994, "width_percent": 0.298}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758159, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.292, "width_percent": 0.192}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7584512, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.484, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758653, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.57, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758896, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.733, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7590861, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.82, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759322, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.973, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759492, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.05, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7597258, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.203, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759903, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.28, "width_percent": 0.144}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760133, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.424, "width_percent": 0.221}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7604868, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.645, "width_percent": 0.202}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760745, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.846, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76092, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.962, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.761154, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.096, "width_percent": 0.23}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7615168, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.326, "width_percent": 0.24}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7618532, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.566, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762057, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.662, "width_percent": 0.163}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7623012, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.825, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7624981, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.912, "width_percent": 0.154}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762747, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.065, "width_percent": 0.269}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.763184, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.334, "width_percent": 0.489}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7638159, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.823, "width_percent": 0.25}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764243, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.073, "width_percent": 0.576}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76492, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.649, "width_percent": 0.317}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765317, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.965, "width_percent": 0.202}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76562, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.167, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765828, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.263, "width_percent": 0.182}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766067, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.445, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766227, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.551, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766433, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.676, "width_percent": 0.077}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766627, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.752, "width_percent": 0.365}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7670681, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.117, "width_percent": 0.125}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7672591, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.242, "width_percent": 0.134}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767481, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.376, "width_percent": 0.086}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.76768, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.463, "width_percent": 0.173}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767899, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.635, "width_percent": 0.106}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7680562, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.741, "width_percent": 0.125}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768265, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.866, "width_percent": 0.24}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7686539, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.106, "width_percent": 0.25}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768963, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.355, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769133, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.47, "width_percent": 0.134}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7693532, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.605, "width_percent": 0.134}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7695441, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.739, "width_percent": 0.125}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769743, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.864, "width_percent": 0.278}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770157, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.142, "width_percent": 0.288}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.770612, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.43, "width_percent": 0.393}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7710931, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.823, "width_percent": 0.278}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771461, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.102, "width_percent": 0.182}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7717502, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.284, "width_percent": 0.096}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7719688, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.38, "width_percent": 0.221}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772241, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.601, "width_percent": 0.115}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772416, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.716, "width_percent": 0.125}, {"sql": "select distinct `tujuan` from `histori_perjalanan_kapal` where `kapal_id` = ? and `tujuan` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.772716, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.841, "width_percent": 0.163}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793209, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.004, "width_percent": 0.509}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793847, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.512, "width_percent": 0.182}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7944992, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.695, "width_percent": 0.307}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.794929, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.002, "width_percent": 0.125}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795108, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.127, "width_percent": 0.144}, {"sql": "select * from `users` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.827004, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.271, "width_percent": 0.729}]}, "models": {"data": {"App\\Models\\ChecklistDataEngine": {"value": 273, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=1", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "?"}}, "App\\Models\\HistoriPerjalananKapal": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FHistoriPerjalananKapal.php&line=1", "ajax": false, "filename": "HistoriPerjalananKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\JenisKapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FJenisKapal.php&line=1", "ajax": false, "filename": "JenisKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 294, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-1506068943 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1506068943\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1750411252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1750411252\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1917262300 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1917262300\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1923532089 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImNrVVc0VXV0cjJvellMK25PUWp2dnc9PSIsInZhbHVlIjoibkVTeGVhOHlOUkx6SXVheUthZTlReGhSTkpJeUFLWmc2UUp6QlZTUEY2WVYxWGxBS2F1RUN5SGxGS3J2K2tIdDlwMzMxeS9XVFk2ZTl5NUJyMGhxUHJQSk5CanFSSmxNbWRjNzBkZi80aitBQVFFRGhEWDFYVXI3S2FLbXhXajEiLCJtYWMiOiJkMTRhOGE1MmJmYzNkMjg1NjI4NzZjMTU5NjdlMGM1NGE0NWE2NTY5NzMxYjI1YzRjNWE4NDc1OWM3YjY4MjEwIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6InBuQlgxRnpaTlcvR3Awb0duZjBpOGc9PSIsInZhbHVlIjoiRjhQWHlOMjZBZjY5ejBKcGhxc0dtTzlEcGt3UzhCWUlXMnZkQkowQU5WQ1dFZ0RYRXduU2JYZnlzVjdqTzg1QWliVnlSZHhUR1pKRUU4c0RGcmdIaDRORGl1QlVtUlJVb29vUk5GTjVEN3RNTTBIYzBoR1hFZnhRNXNDd1pWVVUiLCJtYWMiOiJlMzA4MTMwOWU5ZjI2ZDhkZjZhZGVkZmMzNWNlMWFhNjk2YTdkYWRhNGNhNjllZTQ3MDliZjZhOTYxMmJmM2U5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923532089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-34406030 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34406030\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2045309870 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:09:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNQaWdEOFF6bi9LQ1BUMURpZjhHdXc9PSIsInZhbHVlIjoiVlVWZEMvOW1LUWRBbFlNWXJwZHdYRHM2c3ZQU2VPN1FEbkhJaEdzV2RZT01INUN6ZGE4TFJRVlZLWVJiZC9oOEFudDUyRlRhMndJZlZSYlBEZEgrbVkrM2xLRk5OUzZQQTlOZURxcE1Oa0x3MDNZb1VwdUhJOHpPYXF3MUxSSFEiLCJtYWMiOiJmZDU4MmYyZWQxNjY2ZWQzODBjZmVkOTViYTIyOGMzNGZjMzQ5ZjcxNzJmZDgwMTIxMDdiNjhlNTFjZjU2MDljIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlR0WmtqeW9sU0V1VzFySXpiRDRkVnc9PSIsInZhbHVlIjoiZyswbHRFTTVIS25nSktVb3p1aExKUllSTkZzQzVRZHl3cU1mOFJtZHlXdXNEeDNWU1pNS24yL3JOaGwwSXhDcXlaUmZMd3dKSDZ2NFc2SGpCOUx6QW1PN3ZFclRRVnQzMmNQVE82K2FVeS9EbERXZGJLOGpLZzZuTjd3NzVOZmsiLCJtYWMiOiI0ZDQyYTlhNTZhNDBmYzc4MDQ3MWY4MDc2ZmQ5MmZiOGE1MTA3OWUwMjVlMjgzNWFkNTA3YzZlNjlkNmYyZjZlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNQaWdEOFF6bi9LQ1BUMURpZjhHdXc9PSIsInZhbHVlIjoiVlVWZEMvOW1LUWRBbFlNWXJwZHdYRHM2c3ZQU2VPN1FEbkhJaEdzV2RZT01INUN6ZGE4TFJRVlZLWVJiZC9oOEFudDUyRlRhMndJZlZSYlBEZEgrbVkrM2xLRk5OUzZQQTlOZURxcE1Oa0x3MDNZb1VwdUhJOHpPYXF3MUxSSFEiLCJtYWMiOiJmZDU4MmYyZWQxNjY2ZWQzODBjZmVkOTViYTIyOGMzNGZjMzQ5ZjcxNzJmZDgwMTIxMDdiNjhlNTFjZjU2MDljIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlR0WmtqeW9sU0V1VzFySXpiRDRkVnc9PSIsInZhbHVlIjoiZyswbHRFTTVIS25nSktVb3p1aExKUllSTkZzQzVRZHl3cU1mOFJtZHlXdXNEeDNWU1pNS24yL3JOaGwwSXhDcXlaUmZMd3dKSDZ2NFc2SGpCOUx6QW1PN3ZFclRRVnQzMmNQVE82K2FVeS9EbERXZGJLOGpLZzZuTjd3NzVOZmsiLCJtYWMiOiI0ZDQyYTlhNTZhNDBmYzc4MDQ3MWY4MDc2ZmQ5MmZiOGE1MTA3OWUwMjVlMjgzNWFkNTA3YzZlNjlkNmYyZjZlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045309870\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2029603791 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029603791\", {\"maxDepth\":0})</script>\n"}}