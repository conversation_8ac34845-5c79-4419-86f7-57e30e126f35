{"__meta": {"id": "Xb619daccb8088c1d4c4c05fb5b001fbe", "datetime": "2025-07-20 08:27:34", "utime": 1752974854.738287, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752974854.483986, "end": 1752974854.738306, "duration": 0.2543201446533203, "duration_str": "254ms", "measures": [{"label": "Booting", "start": 1752974854.483986, "relative_start": 0, "end": 1752974854.692696, "relative_end": 1752974854.692696, "duration": 0.2087101936340332, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752974854.692709, "relative_start": 0.2087230682373047, "end": 1752974854.738309, "relative_end": 2.86102294921875e-06, "duration": 0.045599937438964844, "duration_str": "45.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 23739704, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1752974854.732452, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=11\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:11-14</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PganiSVlgXC4D30XSlNsAso8DM1iy37oy4cwub1G", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1339382627 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1339382627\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-43964131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-43964131\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-971760316 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971760316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-266554851 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjY1WncyaCt1NmtqaDREd0xqc1Z1aWc9PSIsInZhbHVlIjoiTUZQTjJKNlh0QmI3WWdNeDZwenEvUlphekRxelliWVp4cEplczJmdGpRWW44bmFJL3NCRHFUTDlUSklCbFMyUFVocm5aanQ4eURqVkJxRkgwTkJCS1l3bEZvcmJEeXZjVE9tN1hBVUhYdTNCMkszeE9PcjJPLytSblZxVWR3UkgiLCJtYWMiOiJhOGU3ODRjZGEyOTA0NWQyMDIzOWM1ODgxYmNlYWI5MDgyZDZmMjQxZWYxOGQ4ZDA4YThlZjdkNjQyNGU3NjFhIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImE0eklGcUZWOXNSUW5PRjRYRWc5VUE9PSIsInZhbHVlIjoiQkhPV29xUmFTWG45aDRyeVYxZzVGVndPdndoY2hhSW5OdGllMlBGNEdmZUdTNDdPU2RJRlh0YXBZU2dJUVJWTUNFS1VyWEM1WDVuSm1MWXNFSy9SYjgyaU5NL1ZoV0tqeE1nN3B1TmMrOTJWZDVFK05sTTY4dUw2Mm8rU3hmNDMiLCJtYWMiOiI3MTA0YzZkMjdhNTQyYTkxOTlmOTUwMDNjYmQ1ZTJlNmI2YTZhYTVjMjlkZWJmY2FkYjFmNGM3MjZlMmQ3NjRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266554851\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1337110878 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PganiSVlgXC4D30XSlNsAso8DM1iy37oy4cwub1G</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sq62xVXlW8PK4Jrb4pvLusps9mrB6Kpg6FhX8on9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337110878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2119072784 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:27:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InloVFJWUjV0QlVFZ3hmcFlNRllUUFE9PSIsInZhbHVlIjoiU01ySE1lbDNGSFcvaGZSdFp4OFdIQXRjMWNwUTN4YVVVeEdVSGtJcVAxN2tRTms2VDRsTUdBczhFZ0xXVktZQTh4aXVhT0R3ZzB0NlhuQzR4OWFVbFIweVl3dlhOOEVvL05VWSs1cW5uUjdubm01NVU4WWc0eDQxQXM0L0lpajkiLCJtYWMiOiJhZjg1ZGZiZTAzYTEyODViNmQxZTcwNGEyY2ExYzgxYTY4NTZlZDMwMTE0NzU2MDI0OThjOGEwNDQ0Yzg3OWQyIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlAzVlJ0TkZrRGhvSmVwNzdtRHJoU2c9PSIsInZhbHVlIjoiNHZHNkJ4ZlJHcXpkRWJPZGpkTFR5dlh6cS9ZbmVDLzVhbjk1TjNYd0dhWVRCL3djYTYwYjR5MHByS280SmM4WXZhK3hZUE5qenI0bjVRT3VoVUR5TDZxZ05kcWorQ1BkRGhtSGprZ1RCMCtUK0psSlhGNWxHQ1lDalhmcS84Qk8iLCJtYWMiOiI2OGZlZjYwMzdmMjQ2MWE2MTdmYzBkNDM0ZWU2ZDQ1MzIzZTFiZmUyZjJlMTVkNjI5ZWNkN2JkNGJmODFkNzRkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InloVFJWUjV0QlVFZ3hmcFlNRllUUFE9PSIsInZhbHVlIjoiU01ySE1lbDNGSFcvaGZSdFp4OFdIQXRjMWNwUTN4YVVVeEdVSGtJcVAxN2tRTms2VDRsTUdBczhFZ0xXVktZQTh4aXVhT0R3ZzB0NlhuQzR4OWFVbFIweVl3dlhOOEVvL05VWSs1cW5uUjdubm01NVU4WWc0eDQxQXM0L0lpajkiLCJtYWMiOiJhZjg1ZGZiZTAzYTEyODViNmQxZTcwNGEyY2ExYzgxYTY4NTZlZDMwMTE0NzU2MDI0OThjOGEwNDQ0Yzg3OWQyIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlAzVlJ0TkZrRGhvSmVwNzdtRHJoU2c9PSIsInZhbHVlIjoiNHZHNkJ4ZlJHcXpkRWJPZGpkTFR5dlh6cS9ZbmVDLzVhbjk1TjNYd0dhWVRCL3djYTYwYjR5MHByS280SmM4WXZhK3hZUE5qenI0bjVRT3VoVUR5TDZxZ05kcWorQ1BkRGhtSGprZ1RCMCtUK0psSlhGNWxHQ1lDalhmcS84Qk8iLCJtYWMiOiI2OGZlZjYwMzdmMjQ2MWE2MTdmYzBkNDM0ZWU2ZDQ1MzIzZTFiZmUyZjJlMTVkNjI5ZWNkN2JkNGJmODFkNzRkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119072784\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1358776932 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PganiSVlgXC4D30XSlNsAso8DM1iy37oy4cwub1G</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358776932\", {\"maxDepth\":0})</script>\n"}}