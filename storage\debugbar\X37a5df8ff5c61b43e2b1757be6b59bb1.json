{"__meta": {"id": "X37a5df8ff5c61b43e2b1757be6b59bb1", "datetime": "2025-07-20 08:09:07", "utime": **********.216533, "method": "POST", "uri": "/checklist-engine/store-item", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752973746.689743, "end": **********.216556, "duration": 0.5268130302429199, "duration_str": "527ms", "measures": [{"label": "Booting", "start": 1752973746.689743, "relative_start": 0, "end": 1752973746.986403, "relative_end": 1752973746.986403, "duration": 0.2966599464416504, "duration_str": "297ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752973746.986419, "relative_start": 0.2966759204864502, "end": **********.21656, "relative_end": 3.814697265625e-06, "duration": 0.23014092445373535, "duration_str": "230ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26141144, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST checklist-engine/store-item", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\ChecklistEngineController@storeItemPemeriksaan", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.store-item", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1658\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:1658-1713</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02732, "accumulated_duration_str": "27.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.047906, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.053076, "duration": 0.01927, "duration_str": "19.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 70.534}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1676}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.0952551, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:1676", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1676}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1676", "ajax": false, "filename": "ChecklistEngineController.php", "line": "1676"}, "connection": "gema_kapal", "explain": null, "start_percent": 70.534, "width_percent": 2.379}, {"sql": "insert into `hasil_pemeriksaan_engine` (`kapal_id`, `history_id`, `item_id`, `kondisi`, `keterangan`, `tanggal_pemeriksaan`, `created_at`, `updated_at`) values (4, 24, '174', 'bagus', '-', '2025-07-20T08:07', '2025-07-20 08:09:07', '2025-07-20 08:09:07')", "type": "query", "params": [], "bindings": [4, 24, "174", "bagus", "-", "2025-07-20T08:07", "2025-07-20 08:09:07", "2025-07-20 08:09:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1694}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.09796, "duration": 0.0074, "duration_str": "7.4ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:1694", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1694}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1694", "ajax": false, "filename": "ChecklistEngineController.php", "line": "1694"}, "connection": "gema_kapal", "explain": null, "start_percent": 72.914, "width_percent": 27.086}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/store-item", "status_code": "<pre class=sf-dump id=sf-dump-1092013757 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1092013757\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1092593463 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1092593463\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1098867068 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>item_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">174</span>\"\n  \"<span class=sf-dump-key>engine</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SITUASIONAL</span>\"\n  \"<span class=sf-dump-key>kapal_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2NaDK50q4p</span>\"\n  \"<span class=sf-dump-key>kondisi</span>\" => \"<span class=sf-dump-str title=\"5 characters\">bagus</span>\"\n  \"<span class=sf-dump-key>keterangan</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tanggal_pemeriksaan</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-07-20T08:07</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098867068\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1820905962 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">801</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryw1DtOBMkGBzLbG7g</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjNTTllxeEdZNXZwTVA4cllqNWlDSVE9PSIsInZhbHVlIjoicFNFMFZjS1pZNVZybENuZHYzb2VISlB1SVNBNCtUTmpPU1JybzN3aXZ3T20rRlZQdlF2NGpPZ1VBRmZ2U1pLZ2dhVmNNejNXaldFajlscnJia2RLbjBweWxrVS8xd3BVa2RJWm8weVlIZVVKcE96enVpdWhWMXZjQlo4OFBXMXYiLCJtYWMiOiI1N2IwZDAzNzU0YTljYzJjMDFkMjFjY2Q5ZTBkMmFkZTNjMjljOGVjNmZiMTAxZDUzZmQ4YzAzMGVmM2IxNDhmIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImNBMC9DVmxVbGozM05wOHdzdmNqdnc9PSIsInZhbHVlIjoid2lUMnBQbTlnMXQ5VGJ4SDZyOXp6L2h0OCsxM25uR0RkdmQxbTBiVHkvYmRsUm9HbzR3VSsvRXB3c3B6NlZUakFnUDRMQThDaGhncitveGY4aUhEa3MwekQ3YnphNXZ0eDhsRko4TUJmeXVIL2dEK0N2eGlaRi9NVmpZMWdkSzAiLCJtYWMiOiIwMjc1MjRjZDQ4NDE2ODYxYzcxZTdjMzYxYjc3OThjMmNkN2M0YjgzYTUzZTBhZWM5NmMxYjkyODE4MGVjMTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820905962\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-537746133 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:09:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ild6MmlFbVVPMjE1QXhHbjZHajdQS3c9PSIsInZhbHVlIjoidXpQaWN0VkFlaitncytqeCsxTi9iMWtFSm5aRnVNMEFtZlhWZ3B1dFNEY3g0NUZvK3IwYk5JcTJ0dFJYS1VTcnAvZXcwdXJEa0JaTURhOFNwcG1LZ2VZMXFJZmhzMTFTTC95eXFXNllBNWF5RFVPNkRKLzFsWDZvSlJXdEtMK0MiLCJtYWMiOiJmNGM4MjQ2MmI0YzdmY2M2ZTBmODM1OTA4NTE0ODMxMTBiYWRmMTcyMGI5ODcyOWVmZTE1ZGI5YTM2NTI3NGFlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IjBMR2M2SUpFMS9qSkUyMWxINlRuMGc9PSIsInZhbHVlIjoiSExtMkp6UWVERi9DQURXckZKaEd3SFB2VWl2RCtkNlNidmVEN2ZwWnBKb1BNNjVHNVpPOS9KazN3Qmt2ekpnWWQvREFqTWQvQjVablQwb3h1TDltbHJVaUo5aWNlcGk0UFp6cjZPRjF4STVBbVpSMW5iWUlXSlVNSUY4V3p6V1EiLCJtYWMiOiIwZjc1Yjc0OTg2OGJhMjEzMzQ5NjBkOTVkNTMyMzk5ZWZmOTYwNzBjODY2ZTI5MTAzYzI3MzU4OGM5M2NmYzU2IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ild6MmlFbVVPMjE1QXhHbjZHajdQS3c9PSIsInZhbHVlIjoidXpQaWN0VkFlaitncytqeCsxTi9iMWtFSm5aRnVNMEFtZlhWZ3B1dFNEY3g0NUZvK3IwYk5JcTJ0dFJYS1VTcnAvZXcwdXJEa0JaTURhOFNwcG1LZ2VZMXFJZmhzMTFTTC95eXFXNllBNWF5RFVPNkRKLzFsWDZvSlJXdEtMK0MiLCJtYWMiOiJmNGM4MjQ2MmI0YzdmY2M2ZTBmODM1OTA4NTE0ODMxMTBiYWRmMTcyMGI5ODcyOWVmZTE1ZGI5YTM2NTI3NGFlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IjBMR2M2SUpFMS9qSkUyMWxINlRuMGc9PSIsInZhbHVlIjoiSExtMkp6UWVERi9DQURXckZKaEd3SFB2VWl2RCtkNlNidmVEN2ZwWnBKb1BNNjVHNVpPOS9KazN3Qmt2ekpnWWQvREFqTWQvQjVablQwb3h1TDltbHJVaUo5aWNlcGk0UFp6cjZPRjF4STVBbVpSMW5iWUlXSlVNSUY4V3p6V1EiLCJtYWMiOiIwZjc1Yjc0OTg2OGJhMjEzMzQ5NjBkOTVkNTMyMzk5ZWZmOTYwNzBjODY2ZTI5MTAzYzI3MzU4OGM5M2NmYzU2IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537746133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-711524703 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-711524703\", {\"maxDepth\":0})</script>\n"}}