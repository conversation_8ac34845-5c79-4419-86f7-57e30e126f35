{"__meta": {"id": "X1439e1a309420d280ce9b5542dd6f56f", "datetime": "2025-07-20 08:27:40", "utime": 1752974860.137795, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.541297, "end": 1752974860.137822, "duration": 0.5965249538421631, "duration_str": "597ms", "measures": [{"label": "Booting", "start": **********.541297, "relative_start": 0, "end": **********.737392, "relative_end": **********.737392, "duration": 0.19609498977661133, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.737406, "relative_start": 0.19610905647277832, "end": 1752974860.137825, "relative_end": 3.0994415283203125e-06, "duration": 0.4004189968109131, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25695344, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAuth%2FLoginController.php&line=16\" onclick=\"\">app/Http/Controllers/Auth/LoginController.php:16-35</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02188, "accumulated_duration_str": "21.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 167}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 127}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 23}], "start": **********.793842, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:167", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=167", "ajax": false, "filename": "EloquentUserProvider.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `username` = 'admin_kapal' limit 1", "type": "query", "params": [], "bindings": ["admin_kapal"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Auth/LoginController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\Auth\\LoginController.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.798289, "duration": 0.02188, "duration_str": "21.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-639834905 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-639834905\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1712293497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1712293497\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2132352061 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PganiSVlgXC4D30XSlNsAso8DM1iy37oy4cwub1G</span>\"\n  \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"11 characters\">admin_kapal</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132352061\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1716821643 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">86</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://sikapal.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InloVFJWUjV0QlVFZ3hmcFlNRllUUFE9PSIsInZhbHVlIjoiU01ySE1lbDNGSFcvaGZSdFp4OFdIQXRjMWNwUTN4YVVVeEdVSGtJcVAxN2tRTms2VDRsTUdBczhFZ0xXVktZQTh4aXVhT0R3ZzB0NlhuQzR4OWFVbFIweVl3dlhOOEVvL05VWSs1cW5uUjdubm01NVU4WWc0eDQxQXM0L0lpajkiLCJtYWMiOiJhZjg1ZGZiZTAzYTEyODViNmQxZTcwNGEyY2ExYzgxYTY4NTZlZDMwMTE0NzU2MDI0OThjOGEwNDQ0Yzg3OWQyIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IlAzVlJ0TkZrRGhvSmVwNzdtRHJoU2c9PSIsInZhbHVlIjoiNHZHNkJ4ZlJHcXpkRWJPZGpkTFR5dlh6cS9ZbmVDLzVhbjk1TjNYd0dhWVRCL3djYTYwYjR5MHByS280SmM4WXZhK3hZUE5qenI0bjVRT3VoVUR5TDZxZ05kcWorQ1BkRGhtSGprZ1RCMCtUK0psSlhGNWxHQ1lDalhmcS84Qk8iLCJtYWMiOiI2OGZlZjYwMzdmMjQ2MWE2MTdmYzBkNDM0ZWU2ZDQ1MzIzZTFiZmUyZjJlMTVkNjI5ZWNkN2JkNGJmODFkNzRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716821643\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-438976728 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PganiSVlgXC4D30XSlNsAso8DM1iy37oy4cwub1G</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sq62xVXlW8PK4Jrb4pvLusps9mrB6Kpg6FhX8on9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438976728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1300973957 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:27:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNibTU5SVQ4WGdGdlZIdU5Lc1lDTmc9PSIsInZhbHVlIjoiR1ZvOUhjSXZlN2czVmloRjd0eUVTY29DSmZrbFdDWXIrSGFIZllHbTZVQXh2MUh0NlJVQXp6NEZ6b1JtZ3NCdWdFdm53Vng0QmYwejBpdjNYdytDZTlCZ3VFWTNJNVNJR1ZwUldST2JZTVZKcEY4cTJ2NmptU3pzYmRmcHhBT2wiLCJtYWMiOiI5MDJmMTRiNjdkYjI5ODkxMTJlNGE5NzRlZjA5ZTc1ZGRhY2RiZWZkMmFhYmJkNjc0NzAzOTMxNzUxMDhhNTYwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImF2QWkwTGhXU09oWGpiYTdSZ1JJQnc9PSIsInZhbHVlIjoiWTROK3NEY3lXcnhXajlOR05PcVpLWnhNa1VmNU4rdVJydXNLekIzV0hJUEdpMStac0E4YTQ2ZThXOGd1eGx1Q3RqcU9LTDkvemc5amJubUhiMUhMUUppU2YxdW1VUEpQWlJLQUJEanE1amVkWHk1YlhBcjkwMDIxalBGeHhMSDkiLCJtYWMiOiI0NWJjODgxYzEzZjkxZDZjZTU2MjUzNmRkOTE1M2ZjZTQ4NzFkYTZiMWM2ZmMzMzQ5YjY3MWRlOWRlMjBmZDc5IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNibTU5SVQ4WGdGdlZIdU5Lc1lDTmc9PSIsInZhbHVlIjoiR1ZvOUhjSXZlN2czVmloRjd0eUVTY29DSmZrbFdDWXIrSGFIZllHbTZVQXh2MUh0NlJVQXp6NEZ6b1JtZ3NCdWdFdm53Vng0QmYwejBpdjNYdytDZTlCZ3VFWTNJNVNJR1ZwUldST2JZTVZKcEY4cTJ2NmptU3pzYmRmcHhBT2wiLCJtYWMiOiI5MDJmMTRiNjdkYjI5ODkxMTJlNGE5NzRlZjA5ZTc1ZGRhY2RiZWZkMmFhYmJkNjc0NzAzOTMxNzUxMDhhNTYwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImF2QWkwTGhXU09oWGpiYTdSZ1JJQnc9PSIsInZhbHVlIjoiWTROK3NEY3lXcnhXajlOR05PcVpLWnhNa1VmNU4rdVJydXNLekIzV0hJUEdpMStac0E4YTQ2ZThXOGd1eGx1Q3RqcU9LTDkvemc5amJubUhiMUhMUUppU2YxdW1VUEpQWlJLQUJEanE1amVkWHk1YlhBcjkwMDIxalBGeHhMSDkiLCJtYWMiOiI0NWJjODgxYzEzZjkxZDZjZTU2MjUzNmRkOTE1M2ZjZTQ4NzFkYTZiMWM2ZmMzMzQ5YjY3MWRlOWRlMjBmZDc5IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300973957\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1809516573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809516573\", {\"maxDepth\":0})</script>\n"}}