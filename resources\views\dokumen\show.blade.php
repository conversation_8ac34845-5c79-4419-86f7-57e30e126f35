@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header {{ $kapal->jenis_kapal_id == 1 ? 'bg-gradient-primary' : 'bg-gradient-warning' }} text-white py-3">
                    <div class="d-flex align-items-center">
                        <a href="{{ route('dashboard') }}" class="btn btn-link text-white p-0 me-3">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <div>
                            <h5 class="card-title mb-0">
                                <i class="fas {{ $kapal->jenis_kapal_id == 1 ? 'fa-ship' : 'fa-anchor' }} me-2"></i>
                                Dokumen {{ $kapal->nama }}
                            </h5>
                            <small class="text-white-50">
                                {{ $kapal->jenis_kapal_id == 1 ? 'Tugboat' : 'Tongkang' }}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- Detail Kapal -->
                    <div class="detail-kapal mb-4">
                        <h5 class="border-bottom pb-2 mb-3">Detail Kapal</h5>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">GT/NT</label>
                                    <span class="fw-medium">{{ $kapal->gt_nt ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">Port of Registry</label>
                                    <span class="fw-medium">{{ $kapal->port_of_registry ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">Tanda Pendaftaran</label>
                                    <span class="fw-medium">{{ $kapal->tanda_pendaftaran ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">Call Sign</label>
                                    <span class="fw-medium">{{ $kapal->call_sign ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">IMO</label>
                                    <span class="fw-medium">{{ $kapal->imo ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">Mesin</label>
                                    <span class="fw-medium">{{ $kapal->mesin ?: '-' }}</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-item">
                                    <label class="text-muted small d-block">Tanda Selar</label>
                                    <span class="fw-medium">{{ $kapal->tanda_selar ?: '-' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="border-bottom pb-2 mb-3">Dokumen Kapal</h5>
                    
                    <!-- Search & Filter Section -->
                    <div class="search-filter-section mb-4">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-6">
                                <div class="search-box">
                                    <label class="form-label small text-muted">Pencarian Dokumen</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-white border-end-0">
                                            <i class="fas fa-search text-muted"></i>
                                        </span>
                                        <input type="text" 
                                               class="form-control border-start-0 ps-0" 
                                               id="searchDokumen" 
                                               placeholder="Cari berdasarkan nama file, status, lokasi...">
                                        <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="filter-status">
                                    <label class="form-label small text-muted">Filter Status</label>
                                    <select class="form-select" id="filterStatus">
                                        <option value="">Semua Status</option>
                                        <option value="Berlaku">Berlaku</option>
                                        <option value="Tidak Berlaku">Tidak Berlaku</option>
                                        <option value="Belum diatur">Belum diatur</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex gap-2">
                                    <div class="filter-checklist">
                                        <label class="form-label small text-muted">Filter Checklist</label>
                                        <select class="form-select" id="filterChecklist">
                                            <option value="">Semua</option>
                                            <option value="captain">Captain ✓</option>
                                            <option value="chief">Chief ✓</option>
                                            <option value="both">Keduanya ✓</option>
                                            <option value="none">Belum ada ✓</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Search Results Info -->
                        <div class="search-results-info mt-3" style="display: none;">
                            <div class="alert alert-info py-2 mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <span id="resultsText">Menampilkan semua dokumen</span>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" id="resetFilters">
                                    <i class="fas fa-undo me-1"></i>Reset Filter
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mb-3">
                        <a href="{{ route('dokumen.download', $kapal->id) }}" class="btn btn-outline-success">
                            <i class="fas fa-download me-2"></i>
                            Download List Dokumen
                        </a>
                    </div>

                    @if(auth()->user()->isAdminKantor())
                    <form class="upload-dokumen-form mb-4" data-kapal-id="{{$kapal->id}}">
                        @csrf
                        <div class="upload-area p-4 bg-light rounded-3 border-2 border-dashed">
                            <div class="text-center mb-4">
                                <div class="display-4 text-primary mb-3">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h4 class="mb-2">Upload Dokumen Baru</h4>
                                <p class="text-muted">Upload file PDF dengan ukuran maksimal 25MB</p>
                            </div>
                            <div class="row justify-content-center">
                                <div class="col-md-10">
                                    <div class="mb-3">
                                        <input type="file" class="form-control" name="dokumen" accept=".pdf" required>
                                    </div>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Status Sertifikat</label>
                                            <input type="text" class="form-control" name="sertifikat_status">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Diberikan di</label>
                                            <input type="text" class="form-control" name="diberikan_di">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Tanggal Diberikan</label>
                                            <input type="date" class="form-control" name="tanggal_diberikan">
                                        </div>
                                        <div class="col-md-6 berlaku-sampai-wrapper">
                                            <label class="form-label">Berlaku Sampai</label>
                                            <input type="date" class="form-control" name="berlaku_sampai">
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input type="hidden" name="berlaku_selamanya" value="0">
                                                <input class="form-check-input" type="checkbox" name="berlaku_selamanya" value="1" id="berlakuSelamanya">
                                                <label class="form-check-label" for="berlakuSelamanya">
                                                    Berlaku Selamanya
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="hidden" name="check_list_captain" value="0">
                                                <input class="form-check-input" type="checkbox" name="check_list_captain" value="1" id="checklistCaptain">
                                                <label class="form-check-label" for="checklistCaptain">
                                                    Checklist Captain
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="hidden" name="check_list_chief" value="0">
                                                <input class="form-check-input" type="checkbox" name="check_list_chief" value="1" id="checklistChief">
                                                <label class="form-check-label" for="checklistChief">
                                                    Checklist Chief
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check">
                                                <input type="hidden" name="is_endorsed" value="0">
                                                <input class="form-check-input" type="checkbox" name="is_endorsed" value="1" id="isEndorsed">
                                                <label class="form-check-label" for="isEndorsed">
                                                    Dokumen Memiliki Tanggal Endorse
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 tanggal-endorse-wrapper d-none">
                                            <label class="form-label">Tanggal Endorse</label>
                                            <input type="date" class="form-control" name="tanggal_endorse">
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-success px-5">
                                            <i class="fas fa-upload me-2"></i>
                                            Upload
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Admin Kapal:</strong> Anda dapat melihat dokumen dan melakukan checklist, tetapi tidak dapat menambah, mengedit, atau menghapus dokumen.
                    </div>
                    @endif

                    <div class="dokumen-items">
                        @if($kapal->dokumen->count() > 0)
                            <div class="list-group">
                                @foreach($kapal->dokumen as $dokumen)
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center p-3" 
                                         id="dokumen-{{$dokumen->id}}">
                                        <a href="{{ Storage::url($dokumen->file_path) }}" 
                                           class="text-decoration-none d-flex align-items-center flex-grow-1"
                                           target="_blank">
                                            <div class="document-icon me-3">
                                                <i class="fas fa-file-pdf text-danger fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $dokumen->nama_file }}</h6>
                                                <small class="text-muted d-block mb-1">
                                                    Diupload pada {{ $dokumen->created_at->format('d M Y H:i') }}
                                                </small>
                                                <div class="dokumen-details">
                                                    @if($dokumen->sertifikat_status)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-certificate me-1"></i> {{ $dokumen->sertifikat_status }}
                                                    </small>
                                                    @endif
                                                    @if($dokumen->diberikan_di)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-map-marker-alt me-1"></i> {{ $dokumen->diberikan_di }}
                                                    </small>
                                                    @endif
                                                    @if($dokumen->tanggal_diberikan)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-calendar-check me-1"></i> Diberikan: {{ $dokumen->tanggal_diberikan->format('d/m/Y') }}
                                                    </small>
                                                    @endif
                                                    @if($dokumen->is_endorsed && $dokumen->tanggal_endorse)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-stamp me-1"></i> Endorse: {{ $dokumen->tanggal_endorse->format('d/m/Y') }}
                                                        @if($dokumen->durasi_endorse)
                                                        <span class="text-primary ms-1">({{ $dokumen->durasi_endorse }} sejak diberikan)</span>
                                                        @endif
                                                    </small>
                                                    @endif
                                                    @if($dokumen->berlaku_selamanya)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-infinity me-1"></i> Berlaku Selamanya
                                                    </small>
                                                    @elseif($dokumen->berlaku_sampai)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-calendar-alt me-1"></i> Berlaku sampai: {{ $dokumen->berlaku_sampai->format('d/m/Y') }}
                                                        @if($dokumen->durasi_berlaku)
                                                        <span class="text-primary ms-1">({{ $dokumen->durasi_berlaku }} {{ $dokumen->is_endorsed ? 'sejak endorse' : 'sejak diberikan' }})</span>
                                                        @if($dokumen->sisa_waktu_berlaku)
                                                        <span class="{{ $dokumen->sisa_waktu_berlaku['color'] }} ms-1">({{ $dokumen->sisa_waktu_berlaku['text'] }})</span>
                                                        @endif
                                                        @endif
                                                    </small>
                                                    @endif
                                                </div>
                                                <small class="badge 
                                                    {{ $dokumen->keterangan === 'Berlaku' ? 'bg-success' : 
                                                       ($dokumen->keterangan === 'Tidak Berlaku' ? 'bg-danger' : 
                                                       ($dokumen->keterangan === 'Belum diatur' ? 'bg-danger' : 'bg-info')) }}">
                                                    {{ $dokumen->keterangan }}
                                                </small>
                                                <div class="mt-1">
                                                    @if($dokumen->is_endorsed)
                                                    <span class="badge bg-info" title="Tanggal Endorse: {{ $dokumen->tanggal_endorse?->format('d/m/Y') }}">
                                                        <i class="fas fa-stamp me-1"></i>Endorsed
                                                    </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </a>
                                        <div class="d-flex align-items-center gap-2">
                                            <!-- Checklist Buttons -->
                                            <div class="checklist-buttons d-flex">
                                                <button class="btn btn-sm toggle-checklist {{ $dokumen->check_list_captain ? 'btn-captain-checked' : 'btn-captain-unchecked' }}" 
                                                        data-id="{{ $dokumen->id }}" 
                                                        data-type="captain"
                                                        title="Toggle Checklist Captain">
                                                    <i class="fas {{ $dokumen->check_list_captain ? 'fa-user-check' : 'fa-user' }}"></i>
                                                    <span class="ms-1">C</span>
                                                </button>
                                                <button class="btn btn-sm toggle-checklist {{ $dokumen->check_list_chief ? 'btn-chief-checked' : 'btn-chief-unchecked' }}" 
                                                        data-id="{{ $dokumen->id }}" 
                                                        data-type="chief"
                                                        title="Toggle Checklist Chief Engineer">
                                                    <i class="fas {{ $dokumen->check_list_chief ? 'fa-hard-hat' : 'fa-hard-hat' }}"></i>
                                                    <span class="ms-1">E</span>
                                                </button>
                                            </div>
                                            <!-- Action Buttons -->
                                            <div class="btn-group">
                                                @if(auth()->user()->isAdminKantor())
                                                <button class="btn btn-outline-primary btn-sm edit-dokumen"
                                                        data-id="{{ $dokumen->id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="Edit Dokumen">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm delete-dokumen"
                                                        data-id="{{ $dokumen->id }}"
                                                        data-bs-toggle="tooltip"
                                                        title="Hapus Dokumen">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-5">
                                <div class="display-1 text-muted mb-4">
                                    <i class="fas fa-file-upload"></i>
                                </div>
                                <h4 class="text-muted">Belum ada dokumen yang diupload</h4>
                                <p class="text-muted">Upload dokumen pertama Anda dengan menggunakan form di atas</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail/Edit Dokumen -->
<div class="modal fade" id="detailDokumenModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Dokumen</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editDokumenForm">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="dokumen_id" id="dokumenId">
                    <input type="hidden" name="berlaku_selamanya" value="0">
                    <input type="hidden" name="check_list_captain" value="0">
                    <input type="hidden" name="check_list_chief" value="0">
                    <input type="hidden" name="is_endorsed" value="0">
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="current-file mb-3">
                                <label class="form-label d-block">File Saat Ini:</label>
                                <a href="" target="_blank" id="currentFile" class="btn btn-outline-primary">
                                    <i class="fas fa-file-pdf me-2"></i>
                                    <span></span>
                                </a>
                            </div>
                            <div class="new-file">
                                <label class="form-label">Upload File Baru (Opsional):</label>
                                <input type="file" class="form-control" name="dokumen" accept=".pdf">
                                <small class="text-muted">Biarkan kosong jika tidak ingin mengubah file</small>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Status Sertifikat</label>
                            <input type="text" class="form-control" name="sertifikat_status" id="editSertifikatStatus">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Diberikan di</label>
                            <input type="text" class="form-control" name="diberikan_di" id="editDiberikanDi">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tanggal Diberikan</label>
                            <input type="date" class="form-control" name="tanggal_diberikan" id="editTanggalDiberikan">
                        </div>
                        <div class="col-md-6 berlaku-sampai-wrapper">
                            <label class="form-label">Berlaku Sampai</label>
                            <input type="date" class="form-control" name="berlaku_sampai" id="editBerlakuSampai">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="berlaku_selamanya" value="1" id="editBerlakuSelamanya">
                                <label class="form-check-label" for="editBerlakuSelamanya">
                                    Berlaku Selamanya
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="check_list_captain" value="1" id="editChecklistCaptain">
                                <label class="form-check-label" for="editChecklistCaptain">
                                    Checklist Captain
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="check_list_chief" value="1" id="editChecklistChief">
                                <label class="form-check-label" for="editChecklistChief">
                                    Checklist Chief
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_endorsed" value="1" id="editIsEndorsed">
                                <label class="form-check-label" for="editIsEndorsed">
                                    Dokumen Memiliki Tanggal Endorse
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 tanggal-endorse-wrapper d-none">
                            <label class="form-label">Tanggal Endorse</label>
                            <input type="date" class="form-control" name="tanggal_endorse" id="editTanggalEndorse">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="saveDokumen">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #1a2980, #26d0ce);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f46b45, #eea849);
}

.border-dashed {
    border-style: dashed !important;
}

.upload-area {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.upload-area:hover {
    background-color: #fff !important;
    box-shadow: 0 0 15px rgba(0,0,0,0.1);
}

.upload-area .form-control {
    background-color: #fff;
}

.upload-area .form-label {
    font-weight: 500;
    color: #495057;
}

.upload-area .form-check-label {
    color: #495057;
}

.document-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.list-group-item {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem !important;
}

.list-group-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-left: 3px solid {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }};
}

.list-group-item .btn {
    opacity: 1;
    transition: all 0.3s ease;
}

.list-group-item:hover .btn {
    opacity: 1;
}

.display-4 {
    font-size: 3rem;
}

.display-1 {
    font-size: 5rem;
    opacity: 0.1;
}

.dokumen-details {
    font-size: 0.85rem;
    margin: 0.5rem 0;
}

.dokumen-details small {
    margin-bottom: 0.25rem;
}

.dokumen-details i {
    width: 16px;
    text-align: center;
}

.badge {
    font-weight: 500;
}

.badge i {
    font-size: 0.85em;
}

.detail-kapal {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
}

.detail-item {
    margin-bottom: 0.5rem;
}

.detail-item label {
    margin-bottom: 0.25rem;
    color: #6c757d;
}

.detail-item span {
    color: #2d3748;
}

.border-bottom {
    border-bottom: 2px solid #e9ecef !important;
}

.list-group-item .btn-group {
    opacity: 1;
    transition: all 0.3s ease;
}

.list-group-item:hover .btn-group {
    opacity: 1;
}

.d-flex.align-items-center.gap-2 {
    min-width: 320px;
}

.checklist-buttons {
    border-right: 2px solid #dee2e6;
    padding-right: 1rem;
    margin-right: 0.75rem;
}

.badge.bg-primary {
    background-color: {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }} !important;
}

.btn-outline-primary {
    color: {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }};
    border-color: {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }};
}

.btn-outline-primary:hover {
    background-color: {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }};
    border-color: {{ $kapal->jenis_kapal_id == 1 ? '#1a2980' : '#f46b45' }};
    color: #fff;
}

.btn-outline-success {
    color: #198754;
    border-color: #198754;
    transition: all 0.3s ease;
}

.btn-outline-success:hover {
    background-color: #198754;
    border-color: #198754;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.toggle-checklist {
    transition: all 0.3s ease;
    font-size: 0.9rem;
    padding: 0.75rem 1rem;
    font-weight: 700;
    border-radius: 0.5rem;
    min-width: 70px;
    min-height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-checklist:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Captain Styles - Blue */
.btn-captain-checked {
    background-color: #0066cc;
    border-color: #0066cc;
    color: #fff;
}

.btn-captain-checked:hover {
    background-color: #0052a3;
    border-color: #0052a3;
    color: #fff;
}

.btn-captain-unchecked {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-captain-unchecked:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

/* Chief Engineer Styles - Orange */
.btn-chief-checked {
    background-color: #fd7e14;
    border-color: #fd7e14;
    color: #fff;
}

.btn-chief-checked:hover {
    background-color: #e55d00;
    border-color: #e55d00;
    color: #fff;
}

.btn-chief-unchecked {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-chief-unchecked:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.checklist-buttons {
    opacity: 1;
    gap: 0.75rem !important;
}

.list-group-item:hover .checklist-buttons {
    opacity: 1;
}

/* Icons untuk status yang lebih jelas */
.btn-captain-checked i {
    color: #fff;
    font-size: 1.1rem;
}

.btn-captain-unchecked i {
    color: #6c757d;
    font-size: 1.1rem;
}

.btn-chief-checked i {
    color: #fff;
    font-size: 1.1rem;
}

.btn-chief-unchecked i {
    color: #6c757d;
    font-size: 1.1rem;
}

/* Membuat tombol lebih menonjol */
.toggle-checklist {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-checklist:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Layout yang lebih rapi */
.checklist-buttons .btn {
    white-space: nowrap;
}

.btn-group .btn {
    margin-left: 0.25rem;
}

/* Search & Filter Styles */
.search-filter-section {
    background: var(--bg-secondary, #f8f9fa);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color, #e9ecef);
    transition: all 0.3s ease;
}

.search-box .input-group-text {
    border-right: none;
}

.search-box .form-control {
    border-left: none;
    box-shadow: none;
}

.search-box .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.search-box .input-group-text {
    background-color: #fff;
    border-color: #ced4da;
}

.search-box .form-control:focus + .btn {
    border-color: #86b7fe;
}

#clearSearch {
    border-left: none;
}

.filter-status .form-select,
.filter-checklist .form-select {
    font-size: 0.9rem;
}

.search-results-info {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dokumen-item-hidden {
    display: none !important;
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.highlight {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    font-weight: 600;
}

/* ========== DARK MODE SPECIFIC STYLES FOR DOCUMENT PAGE ========== */

/* Document Details Section */
[data-bs-theme="dark"] .detail-kapal {
    background-color: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
}

[data-bs-theme="dark"] .detail-item label {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .detail-item span {
    color: var(--text-primary) !important;
}

/* Upload Area */
[data-bs-theme="dark"] .upload-area {
    background-color: var(--surface-card) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .upload-area:hover {
    background-color: var(--bg-tertiary) !important;
    box-shadow: 0 0 15px var(--shadow-sm) !important;
}

[data-bs-theme="dark"] .upload-area .form-control {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .upload-area .form-label {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .upload-area .form-check-label {
    color: var(--text-primary) !important;
}

/* Document Items */
[data-bs-theme="dark"] .list-group-item {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .list-group-item:hover {
    background: var(--bg-tertiary) !important;
    border-left: 3px solid var(--primary-color) !important;
    box-shadow: 0 4px 6px var(--shadow-sm) !important;
}

[data-bs-theme="dark"] .list-group-item a {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .list-group-item a:hover {
    color: var(--primary-color) !important;
}

/* Document Details */
[data-bs-theme="dark"] .dokumen-details small {
    color: var(--text-secondary) !important;
}

/* Captain/Chief Buttons - Dark Mode Friendly Colors */
[data-bs-theme="dark"] .btn-captain-checked {
    background-color: #2563eb !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .btn-captain-checked:hover {
    background-color: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .btn-captain-unchecked {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .btn-captain-unchecked:hover {
    background-color: var(--bg-secondary) !important;
    border-color: #2563eb !important;
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .btn-chief-checked {
    background-color: #ea580c !important;
    border-color: #ea580c !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .btn-chief-checked:hover {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .btn-chief-unchecked {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-muted) !important;
}

[data-bs-theme="dark"] .btn-chief-unchecked:hover {
    background-color: var(--bg-secondary) !important;
    border-color: #ea580c !important;
    color: var(--text-secondary) !important;
}

/* Checklist buttons container */
[data-bs-theme="dark"] .checklist-buttons {
    border-right-color: var(--border-color) !important;
}

/* Document status badges */
[data-bs-theme="dark"] .badge.bg-success {
    background-color: #16a34a !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .badge.bg-danger {
    background-color: #dc2626 !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .badge.bg-info {
    background-color: #0891b2 !important;
    color: #ffffff !important;
}

/* Empty state */
[data-bs-theme="dark"] .empty-state {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .empty-state i {
    color: var(--text-muted) !important;
}

/* No results state */
[data-bs-theme="dark"] .no-results {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .no-results i {
    color: var(--text-muted) !important;
}

/* Search results highlight */
[data-bs-theme="dark"] .highlight {
    background-color: rgba(234, 179, 8, 0.3) !important;
    color: var(--text-primary) !important;
}

/* Gradients for card headers */
[data-bs-theme="dark"] .bg-gradient-primary {
    background: linear-gradient(135deg, #1e40af, #0891b2) !important;
}

[data-bs-theme="dark"] .bg-gradient-warning {
    background: linear-gradient(135deg, #ea580c, #f59e0b) !important;
}

/* File icons */
[data-bs-theme="dark"] .document-icon .fa-file-pdf {
    color: #ef4444 !important;
}

/* Better focus states */
[data-bs-theme="dark"] .toggle-checklist:focus {
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25) !important;
    outline: none !important;
}

/* Search input group styling */
[data-bs-theme="dark"] .search-box .input-group-text {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .search-box .form-control {
    background-color: var(--surface-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .search-box .form-control:focus {
    background-color: var(--surface-card) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .search-box .form-control::placeholder {
    color: var(--text-muted) !important;
}

/* Filter selects */
[data-bs-theme="dark"] .filter-status .form-select,
[data-bs-theme="dark"] .filter-checklist .form-select {
    background-color: var(--surface-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .filter-status .form-select:focus,
[data-bs-theme="dark"] .filter-checklist .form-select:focus {
    background-color: var(--surface-card) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25) !important;
}

/* Search results info */
[data-bs-theme="dark"] .search-results-info .alert-info {
    background-color: rgba(8, 145, 178, 0.1) !important;
    border-color: rgba(8, 145, 178, 0.3) !important;
    color: #0891b2 !important;
}

/* Modal improvements */
[data-bs-theme="dark"] .modal-content {
    background-color: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 10px 25px var(--shadow-md) !important;
}

[data-bs-theme="dark"] .modal-header {
    background-color: var(--bg-secondary) !important;
    border-bottom: 1px solid var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-title {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Page transitions */
[data-bs-theme="dark"] .fade-up {
    background-color: var(--bg-primary) !important;
}

/* Ensure all text is readable */
[data-bs-theme="dark"] h1, [data-bs-theme="dark"] h2, [data-bs-theme="dark"] h3, 
[data-bs-theme="dark"] h4, [data-bs-theme="dark"] h5, [data-bs-theme="dark"] h6 {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] p, [data-bs-theme="dark"] span, [data-bs-theme="dark"] div {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .small, [data-bs-theme="dark"] small {
    color: var(--text-secondary) !important;
}

</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle document upload
    $('.upload-dokumen-form').on('submit', function(e) {
        e.preventDefault();
        const form = $(this);
        const kapalId = form.data('kapal-id');
        const formData = new FormData(this);
        formData.append('kapal_id', kapalId);
        
        // Show loading state
        const submitButton = form.find('button[type="submit"]');
        const originalButtonText = submitButton.html();
        submitButton.prop('disabled', true);
        submitButton.html('<i class="fas fa-spinner fa-spin me-2"></i> Mengupload...');
        
        $.ajax({
            url: '{{ route("dokumen.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    const dokumenContainer = $('.dokumen-items');
                    const emptyState = dokumenContainer.find('.text-center');
                    
                    // Remove empty state if it exists
                    if (emptyState.length) {
                        emptyState.remove();
                        dokumenContainer.html('<div class="list-group"></div>');
                    }
                    
                    const dokumenHtml = `
                        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center p-3" 
                             id="dokumen-${response.dokumen.id}">
                            <a href="${response.dokumen.file_path}" 
                               class="text-decoration-none d-flex align-items-center flex-grow-1"
                               target="_blank">
                                <div class="document-icon me-3">
                                    <i class="fas fa-file-pdf text-danger fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">${response.dokumen.nama_file}</h6>
                                    <small class="text-muted d-block mb-1">
                                        Baru saja diupload
                                    </small>
                                    <div class="dokumen-details">
                                        ${response.dokumen.sertifikat_status ? 
                                            `<small class="text-muted d-block">
                                                <i class="fas fa-certificate me-1"></i> ${response.dokumen.sertifikat_status}
                                            </small>` : ''}
                                        ${response.dokumen.diberikan_di ? 
                                            `<small class="text-muted d-block">
                                                <i class="fas fa-map-marker-alt me-1"></i> ${response.dokumen.diberikan_di}
                                            </small>` : ''}
                                        ${response.dokumen.tanggal_diberikan ? 
                                            `<small class="text-muted d-block">
                                                <i class="fas fa-calendar-check me-1"></i> Diberikan: ${response.dokumen.tanggal_diberikan}
                                            </small>` : ''}
                                        ${response.dokumen.is_endorsed && response.dokumen.tanggal_endorse ? 
                                            `<small class="text-muted d-block">
                                                <i class="fas fa-stamp me-1"></i> Endorse: ${response.dokumen.tanggal_endorse}
                                                ${response.dokumen.durasi_endorse ? 
                                                `<span class="text-primary ms-1">(${response.dokumen.durasi_endorse} sejak diberikan)</span>` : ''}
                                            </small>` : ''}
                                        ${response.dokumen.berlaku_selamanya ? 
                                            `<small class="text-muted d-block">
                                                <i class="fas fa-infinity me-1"></i> Berlaku Selamanya
                                            </small>` : 
                                            (response.dokumen.berlaku_sampai ? 
                                                `<small class="text-muted d-block">
                                                    <i class="fas fa-calendar-alt me-1"></i> Berlaku sampai: ${response.dokumen.berlaku_sampai}
                                                    ${response.dokumen.durasi_berlaku ? 
                                                    `<span class="text-primary ms-1">(${response.dokumen.durasi_berlaku} ${response.dokumen.is_endorsed ? 'sejak endorse' : 'sejak diberikan'})</span>` : ''}
                                                    ${response.dokumen.sisa_waktu_berlaku ? 
                                                    `<span class="${response.dokumen.sisa_waktu_berlaku.color} ms-1">(${response.dokumen.sisa_waktu_berlaku.text})</span>` : ''}
                                                </small>` : '')}
                                    </div>
                                    <small class="badge ${response.dokumen.keterangan === 'Berlaku' ? 'bg-success' : 
                                        (response.dokumen.keterangan === 'Tidak Berlaku' ? 'bg-danger' : 
                                        (response.dokumen.keterangan === 'Belum diatur' ? 'bg-danger' : 'bg-info'))}">
                                        ${response.dokumen.keterangan}
                                    </small>
                                    <div class="mt-1">
                                        ${response.dokumen.is_endorsed ? 
                                            `<span class="badge bg-info" title="Tanggal Endorse: ${response.dokumen.tanggal_endorse}">
                                                <i class="fas fa-stamp me-1"></i>Endorsed
                                            </span>` : ''}
                                    </div>
                                </div>
                            </a>
                            <div class="d-flex align-items-center gap-2">
                                <!-- Checklist Buttons -->
                                <div class="checklist-buttons d-flex">
                                    <button class="btn btn-sm toggle-checklist ${response.dokumen.check_list_captain ? 'btn-captain-checked' : 'btn-captain-unchecked'}" 
                                            data-id="${response.dokumen.id}" 
                                            data-type="captain"
                                            title="Toggle Checklist Captain">
                                        <i class="fas ${response.dokumen.check_list_captain ? 'fa-user-check' : 'fa-user'}"></i>
                                        <span class="ms-1">C</span>
                                    </button>
                                    <button class="btn btn-sm toggle-checklist ${response.dokumen.check_list_chief ? 'btn-chief-checked' : 'btn-chief-unchecked'}" 
                                            data-id="${response.dokumen.id}" 
                                            data-type="chief"
                                            title="Toggle Checklist Chief Engineer">
                                        <i class="fas fa-hard-hat"></i>
                                        <span class="ms-1">E</span>
                                    </button>
                                </div>
                                <!-- Action Buttons -->
                                <div class="btn-group">
                                    @if(auth()->user()->isAdminKantor())
                                    <button class="btn btn-outline-primary btn-sm edit-dokumen"
                                            data-id="${response.dokumen.id}"
                                            data-bs-toggle="tooltip"
                                            title="Edit Dokumen">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm delete-dokumen"
                                            data-id="${response.dokumen.id}"
                                            data-bs-toggle="tooltip"
                                            title="Hapus Dokumen">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Append new document
                    $('.list-group').prepend(dokumenHtml);
                    
                    // Initialize tooltip for new delete button
                    var tooltip = new bootstrap.Tooltip($(`#dokumen-${response.dokumen.id} [data-bs-toggle="tooltip"]`)[0]);
                    
                    // Clear the file input
                    form.find('input[type="file"]').val('');
                    
                    // Show success message
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    
                    // Initialize delete handler for new document
                    initDeleteHandler();
                    
                    // Re-apply current filters to include new document
                    filterDocuments();
                }
            },
            error: function(xhr) {
                let errorMessage = 'Gagal mengupload dokumen';
                if (xhr.status === 419) {
                    errorMessage = 'Sesi telah berakhir. Silakan muat ulang halaman.';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            complete: function() {
                // Reset submit button
                submitButton.prop('disabled', false);
                submitButton.html(originalButtonText);
            }
        });
    });

    // Initialize delete handlers
    function initDeleteHandler() {
        $('.delete-dokumen').off('click').on('click', function(e) {
            e.preventDefault();
            const btn = $(this);
            const dokumenId = btn.data('id');
            
            Swal.fire({
                title: 'Hapus Dokumen?',
                text: "Dokumen yang dihapus tidak dapat dikembalikan",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: '/dokumen-kapal/delete/' + dokumenId,
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            _method: 'DELETE'
                        },
                        success: function(response) {
                            const dokumenItem = $(`#dokumen-${dokumenId}`);
                            dokumenItem.fadeOut(300, function() {
                                $(this).remove();
                                
                                // Check if there are no more documents
                                if ($('.list-group-item').length === 0) {
                                    $('.dokumen-items').html(`
                                        <div class="text-center py-5">
                                            <div class="display-1 text-muted mb-4">
                                                <i class="fas fa-file-upload"></i>
                                            </div>
                                            <h4 class="text-muted">Belum ada dokumen yang diupload</h4>
                                            <p class="text-muted">Upload dokumen pertama Anda dengan menggunakan form di atas</p>
                                        </div>
                                    `);
                                }
                                
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Berhasil!',
                                    text: 'Dokumen berhasil dihapus',
                                    timer: 1500,
                                    showConfirmButton: false
                                });
                                
                                // Re-apply filters after deletion
                                filterDocuments();
                            });
                        },
                        error: function(xhr) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Gagal menghapus dokumen',
                                timer: 1500,
                                showConfirmButton: false
                            });
                        }
                    });
                }
            });
        });
    }

    // Initialize delete handlers on page load
    initDeleteHandler();

    // Handle toggle checklist
    $(document).on('click', '.toggle-checklist', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const btn = $(this);
        const dokumenId = btn.data('id');
        const type = btn.data('type');
        
        // Show loading state
        const originalHtml = btn.html();
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Loading...');
        
        $.ajax({
            url: `/dokumen-kapal/toggle-checklist/${dokumenId}`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                type: type
            },
            success: function(response) {
                if (response.success) {
                    // Update button appearance berdasarkan status baru
                    const isChecked = response.status[type];
                    
                    if (type === 'captain') {
                        if (isChecked) {
                            btn.removeClass('btn-captain-unchecked').addClass('btn-captain-checked');
                            btn.html('<i class="fas fa-user-check"></i><span class="ms-1">C</span>');
                        } else {
                            btn.removeClass('btn-captain-checked').addClass('btn-captain-unchecked');
                            btn.html('<i class="fas fa-user"></i><span class="ms-1">C</span>');
                        }
                    } else { // chief
                        if (isChecked) {
                            btn.removeClass('btn-chief-unchecked').addClass('btn-chief-checked');
                            btn.html('<i class="fas fa-hard-hat"></i><span class="ms-1">E</span>');
                        } else {
                            btn.removeClass('btn-chief-checked').addClass('btn-chief-unchecked');
                            btn.html('<i class="fas fa-hard-hat"></i><span class="ms-1">E</span>');
                        }
                    }
                    
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: `Checklist ${type === 'captain' ? 'Captain' : 'Chief'} berhasil ${isChecked ? 'diaktifkan' : 'dinonaktifkan'}`,
                        timer: 1500,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                    
                    // Re-apply filters if checklist filter is active
                    if (currentFilters.checklist) {
                        filterDocuments();
                    }
                }
            },
            error: function(xhr) {
                let errorMessage = 'Gagal mengupdate checklist';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: errorMessage,
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            complete: function() {
                btn.prop('disabled', false);
            }
        });
    });

    // Handle berlaku selamanya checkbox
    $('#berlakuSelamanya').on('change', function() {
        const berlakuSampaiWrapper = $('.berlaku-sampai-wrapper');
        if (this.checked) {
            berlakuSampaiWrapper.addClass('d-none');
            $('[name="berlaku_sampai"]').val('');
        } else {
            berlakuSampaiWrapper.removeClass('d-none');
        }
    });

    // Handle is_endorsed checkbox
    $('#isEndorsed').on('change', function() {
        const tanggalEndorseWrapper = $('.tanggal-endorse-wrapper');
        if (this.checked) {
            tanggalEndorseWrapper.removeClass('d-none');
        } else {
            tanggalEndorseWrapper.addClass('d-none');
            $('[name="tanggal_endorse"]').val('');
        }
    });
});

// Handle view dokumen
$('.edit-dokumen').on('click', function() {
    const dokumenId = $(this).data('id');
    
    // Show loading state
    Swal.fire({
        title: 'Memuat...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Fetch dokumen details
    $.get(`/dokumen-kapal/${dokumenId}`, function(response) {
        Swal.close();
        
        const dokumen = response.dokumen;
        
        // Set form values
        $('#dokumenId').val(dokumen.id);
        $('#currentFile span').text(dokumen.nama_file);
        $('#currentFile').attr('href', dokumen.file_path);
        $('#editSertifikatStatus').val(dokumen.sertifikat_status);
        $('#editDiberikanDi').val(dokumen.diberikan_di);
        $('#editTanggalDiberikan').val(dokumen.tanggal_diberikan);
        $('#editBerlakuSampai').val(dokumen.berlaku_sampai);
        $('#editBerlakuSelamanya').prop('checked', dokumen.berlaku_selamanya);
        $('#editChecklistCaptain').prop('checked', dokumen.check_list_captain);
        $('#editChecklistChief').prop('checked', dokumen.check_list_chief);
        $('#editIsEndorsed').prop('checked', dokumen.is_endorsed);
        $('#editTanggalEndorse').val(dokumen.tanggal_endorse);
        
        // Handle visibility
        if (dokumen.berlaku_selamanya) {
            $('.berlaku-sampai-wrapper').addClass('d-none');
        } else {
            $('.berlaku-sampai-wrapper').removeClass('d-none');
        }
        
        if (dokumen.is_endorsed) {
            $('.tanggal-endorse-wrapper').removeClass('d-none');
        } else {
            $('.tanggal-endorse-wrapper').addClass('d-none');
        }
        
        // Show modal
        $('#detailDokumenModal').modal('show');
    }).fail(function() {
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'Gagal memuat detail dokumen'
        });
    });
});

// Handle save changes
$('#saveDokumen').on('click', function() {
    const form = $('#editDokumenForm');
    const dokumenId = $('#dokumenId').val();
    const formData = new FormData(form[0]);
    
    // Show loading state
    const button = $(this);
    const originalText = button.text();
    button.prop('disabled', true).text('Menyimpan...');
    
    $.ajax({
        url: `/dokumen-kapal/update/${dokumenId}`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#detailDokumenModal').modal('hide');
                
                // Update file path dan nama file jika ada file baru
                if (response.dokumen && response.dokumen.file_path) {
                    const dokumenElement = $(`#dokumen-${dokumenId}`);
                    dokumenElement.find('a').attr('href', response.dokumen.file_path);
                    if (response.dokumen.nama_file) {
                        dokumenElement.find('h6').text(response.dokumen.nama_file);
                    }
                }
                
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: response.message,
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    window.location.reload();
                });
            }
        },
        error: function(xhr) {
            let errorMessage = 'Gagal menyimpan perubahan';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: errorMessage
            });
        },
        complete: function() {
            button.prop('disabled', false).text(originalText);
        }
    });
});

// Handle is_endorsed checkbox di modal detail/edit
$('#editIsEndorsed').on('change', function() {
    const tanggalEndorseWrapper = $('#detailDokumenModal .tanggal-endorse-wrapper');
    if (this.checked) {
        tanggalEndorseWrapper.removeClass('d-none');
    } else {
        tanggalEndorseWrapper.addClass('d-none');
        $('#editTanggalEndorse').val('');
    }
});

// ========== SEARCH AND FILTER FUNCTIONALITY ==========
let currentFilters = {
    search: '',
    status: '',
    checklist: ''
};

// Function to highlight search terms
function highlightText(text, searchTerm) {
    if (!searchTerm) return text;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
}

// Function to filter documents
function filterDocuments() {
    const dokumenItems = $('.list-group-item');
    let visibleCount = 0;
    let totalCount = dokumenItems.length;

    dokumenItems.each(function() {
        const $item = $(this);
        let shouldShow = true;

        // Get dokumen data
        const namaFile = $item.find('h6').text().toLowerCase();
        const dokumenDetails = $item.find('.dokumen-details').text().toLowerCase();
        const statusBadge = $item.find('.badge').text().toLowerCase();
        const captainChecked = $item.find('.btn-captain-checked').length > 0;
        const chiefChecked = $item.find('.btn-chief-checked').length > 0;

        // Search filter
        if (currentFilters.search) {
            const searchTerm = currentFilters.search.toLowerCase();
            const searchableContent = namaFile + ' ' + dokumenDetails + ' ' + statusBadge;
            if (!searchableContent.includes(searchTerm)) {
                shouldShow = false;
            }
        }

        // Status filter
        if (currentFilters.status) {
            if (!statusBadge.includes(currentFilters.status.toLowerCase())) {
                shouldShow = false;
            }
        }

        // Checklist filter
        if (currentFilters.checklist) {
            switch (currentFilters.checklist) {
                case 'captain':
                    if (!captainChecked) shouldShow = false;
                    break;
                case 'chief':
                    if (!chiefChecked) shouldShow = false;
                    break;
                case 'both':
                    if (!captainChecked || !chiefChecked) shouldShow = false;
                    break;
                case 'none':
                    if (captainChecked || chiefChecked) shouldShow = false;
                    break;
            }
        }

        if (shouldShow) {
            $item.removeClass('dokumen-item-hidden');
            visibleCount++;

            // Highlight search terms
            if (currentFilters.search) {
                const originalTitle = $item.find('h6').data('original-text') || $item.find('h6').text();
                $item.find('h6').data('original-text', originalTitle);
                $item.find('h6').html(highlightText(originalTitle, currentFilters.search));
            } else {
                // Remove highlights
                const originalTitle = $item.find('h6').data('original-text');
                if (originalTitle) {
                    $item.find('h6').html(originalTitle);
                }
            }
        } else {
            $item.addClass('dokumen-item-hidden');
        }
    });

    // Update results info
    updateResultsInfo(visibleCount, totalCount);

    // Show/hide no results message
    if (visibleCount === 0 && totalCount > 0) {
        showNoResults();
    } else {
        hideNoResults();
    }
}

// Function to update results info
function updateResultsInfo(visible, total) {
    const hasFilters = currentFilters.search || currentFilters.status || currentFilters.checklist;
    const $resultsInfo = $('.search-results-info');
    const $resultsText = $('#resultsText');

    if (hasFilters) {
        $resultsInfo.show();
        if (visible === total) {
            $resultsText.text(`Menampilkan semua ${total} dokumen`);
        } else {
            $resultsText.text(`Menampilkan ${visible} dari ${total} dokumen`);
        }
    } else {
        $resultsInfo.hide();
    }
}

// Function to show no results message
function showNoResults() {
    if (!$('.no-results').length) {
        const noResultsHtml = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h5>Tidak ada dokumen yang ditemukan</h5>
                <p>Coba ubah kata kunci pencarian atau filter yang digunakan</p>
                <button class="btn btn-outline-primary btn-sm" onclick="$('#resetFilters').click()">
                    <i class="fas fa-undo me-1"></i>Reset Filter
                </button>
            </div>
        `;
        $('.dokumen-items').append(noResultsHtml);
    }
}

// Function to hide no results message
function hideNoResults() {
    $('.no-results').remove();
}

// Search input handler with debounce
let searchTimeout;
$('#searchDokumen').on('input', function() {
    currentFilters.search = $(this).val().trim();
    
    // Clear previous timeout
    clearTimeout(searchTimeout);
    
    // Set new timeout for debounced search
    searchTimeout = setTimeout(function() {
        filterDocuments();
    }, 300);
});

// Status filter handler
$('#filterStatus').on('change', function() {
    currentFilters.status = $(this).val();
    filterDocuments();
});

// Checklist filter handler
$('#filterChecklist').on('change', function() {
    currentFilters.checklist = $(this).val();
    filterDocuments();
});

// Clear search button
$('#clearSearch').on('click', function() {
    $('#searchDokumen').val('').focus();
    currentFilters.search = '';
    filterDocuments();
});

// Reset all filters
$('#resetFilters').on('click', function() {
    $('#searchDokumen').val('');
    $('#filterStatus').val('');
    $('#filterChecklist').val('');
    currentFilters = { search: '', status: '', checklist: '' };
    filterDocuments();
});

// Keyboard shortcuts
$(document).on('keydown', function(e) {
    // Ctrl/Cmd + F to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        $('#searchDokumen').focus();
    }
    
    // Escape to clear search when focused on search input
    if (e.key === 'Escape' && $('#searchDokumen').is(':focus')) {
        $('#clearSearch').click();
    }
});

// Auto-focus search on page load
$(document).ready(function() {
    // Add shortcut hint
    $('#searchDokumen').attr('title', 'Tekan Ctrl+F untuk fokus pencarian');
    
    // Initialize filter count
    filterDocuments();
});
</script>
@endpush 