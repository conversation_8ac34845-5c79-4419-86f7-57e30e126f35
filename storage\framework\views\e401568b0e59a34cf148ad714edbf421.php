<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>List <PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 10px;
        }
        .header {
            margin-bottom: 5px;
            border-bottom: 2px solid #000;
            padding-bottom: 2px;
            text-align: center;
            position: relative;
        }
        .company-logo {
            position: absolute;
            left: 0;
            top: 0;
            width: 80px;
        }
        .company-logo img {
            width: 100%;
            height: auto;
        }
        .company-info {
            display: inline-block;
            text-align: center;
            margin: 0 auto;
            padding: 0 80px;
        }
        .company-info h3 {
            margin: 0;
            font-size: 11px;
            line-height: 1.2;
            text-align: center;
        }
        .company-info p {
            margin: 1px auto;
            font-size: 9px;
            line-height: 1.1;
            text-align: center;
        }
        .company-info p:last-child {
            margin-top: 2px;
            display: inline-block;
            width: auto;
        }
        .ship-details {
            clear: both;
            margin-bottom: 5px;
        }
        .ship-info {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 3px;
        }
        .ship-info td {
            padding: 0;
            border: none;
            font-size: 9px;
        }
        .ship-info .label {
            width: 150px;
        }
        .ship-info .separator {
            width: 20px;
            text-align: center;
        }
        .list-title {
            text-align: center;
            font-weight: bold;
            margin: 5px 0;
        }
        table.documents {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }
        .documents th, .documents td {
            border: 1px solid #000;
            padding: 1px 2px;
            text-align: center;
            font-size: 8px;
            vertical-align: middle;
            line-height: 1.1;
        }
        .documents th {
            background-color: #fff;
            font-weight: normal;
        }
        .documents th.sertifikat {
            text-align: center;
            font-weight: bold;
            border-bottom: none;
        }
        .documents th.checklist {
            text-align: center;
        }
        .documents td.checklist-col {
            padding: 0;
        }
        .checklist-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .checklist-table td {
            border: none;
            border-left: 1px solid #000;
            width: 50%;
            padding: 6px;
        }
        .checklist-table td:first-child {
            border-left: none;
        }
        .no-column {
            width: 30px;
        }
        .sertifikat-column {
            width: 30%;
            text-align: left !important;
        }
        .diberikan-column {
            width: 12%;
        }
        .tanggal-column {
            width: 12%;
        }
        .berlaku-column {
            width: 12%;
        }
        .ket-column {
            width: 8%;
        }
        .checklist-column {
            width: 8%;
        }
        .footer {
            width: 100%;
            padding: 0;
            margin-top: 15px;
        }
        .documents td {
            white-space: nowrap;
            text-transform: uppercase;
        }
        .documents td.sertifikat-column {
            text-transform: none;
        }
        .documents tr[style*="background-color: #ffff00"] td {
            background-color: #ffff00;
        }
        .documents tr:nth-child(2) th {
            border-top: none;
        }
        .note {
            margin-bottom: 10px;
        }
        .note-table {
            width: 100%;
            font-size: 9px;
        }
        .note-table td {
            padding: 1px 3px;
        }
        .confirmation-text {
            text-align: center;
            margin: 5px 0;
            font-size: 9px;
        }
        .signature-table {
            width: 100%;
            text-align: center;
            font-size: 9px;
        }
        .signature-space {
            height: 60px;
            vertical-align: bottom;
            padding: 5px;
        }
        .name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .title {
            font-size: 8px;
        }
        .stamp-image {
            max-width: 80px;
            max-height: 40px;
        }
        .signature-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .qr-code {
            width: 40px;
            height: 40px;
            margin-bottom: 5px;
        }
        
        .signature-date {
            text-transform: uppercase;
        }
        
        .signature-table td {
            width: 33.33%;
        }
        
        .content-wrapper {
            position: relative;
            min-height: 100%;
            padding-bottom: 0;
        }
        
        .page-break {
            display: none;
        }
        .documents tr {
            page-break-inside: avoid;
        }
        .documents tr.expired td {
            background-color: #ffff00;
        }
        .documents td.green-text {
            color: #008000;
        }
        
        /* Style untuk nomor halaman */
        .page-number {
            text-align: center;
            font-size: 9px;
            margin-top: 10px;
        }
        .table-footer-space {
            height: 15px;
        }
    </style>
</head>
<body>
    <div class="content-wrapper">
        <div class="header">
            <div class="company-logo">
                <img src="<?php echo e(public_path('images/logo.png')); ?>" alt="Gema Mutiara Marina">
            </div>
            <div class="company-info">
                <h3>Shipping Company</h3>
                <h3>PT.GEMA MUTIARA MARINA</h3>
                <p>Landing Craft, Tug Boat & Barge Service, Supply Vessel , Shipping Agency</p>
                <p>Jl.RE. Martadinata No.1 Bengkulu 38216 Telp.(0736) 51683, 7008415 Facs. (0736) 52832</p>
                <p>E-mail : <EMAIL></p>
            </div>
        </div>

        <?php if($kapal1): ?>
            <div class="ship-details">
                <table class="ship-info">
                    <tr>
                        <td class="label">NAMA KAPAL</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->nama); ?></td>
                        <td class="label">IMO</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->imo ?: '-'); ?></td>
                    </tr>
                    <tr>
                        <td class="label">GT/NT</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->gt_nt ?: '-'); ?></td>
                        <td class="label">MESIN</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->mesin ?: '-'); ?></td>
                    </tr>
                    <tr>
                        <td class="label">CALL SIGN</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->call_sign ?: '-'); ?></td>
                        <td class="label">TANDA SELAR</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->tanda_selar ?: '-'); ?></td>
                    </tr>
                    <tr>
                        <td class="label">PORT OF REGISTRY</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal1->port_of_registry ?: '-'); ?></td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td class="label">TANDA PENDAFTARAN</td>
                        <td class="separator">:</td>
                        <td colspan="4"><?php echo e($kapal1->tanda_pendaftaran ?: '-'); ?></td>
                    </tr>
                </table>
            </div>

            <div class="list-title">LIST DOKUMEN <?php echo e($kapal1->nama); ?></div>

            <?php echo $__env->make('dokumen.pdf.partials.dokumen-table', ['dokumen' => $dokumenKapal1], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php if($kapal2): ?>
            <div style="page-break-before: always;"></div>
            <div class="ship-details">
                <table class="ship-info">
                    <tr>
                        <td class="label">NAMA KAPAL</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal2->nama); ?></td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td class="label">GT/NT</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal2->gt_nt ?: '-'); ?></td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td class="label">PORT OF REGISTRY</td>
                        <td class="separator">:</td>
                        <td><?php echo e($kapal2->port_of_registry ?: '-'); ?></td>
                        <td colspan="3"></td>
                    </tr>
                    <tr>
                        <td class="label">TANDA PENDAFTARAN</td>
                        <td class="separator">:</td>
                        <td colspan="4"><?php echo e($kapal2->tanda_pendaftaran ?: '-'); ?></td>
                    </tr>
                </table>
            </div>

            <div class="list-title">LIST DOKUMEN <?php echo e($kapal2->nama); ?></div>

            <?php echo $__env->make('dokumen.pdf.partials.dokumen-table', ['dokumen' => $dokumenKapal2], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <div class="footer">
                <div class="note">
                    NOTE
                    <table class="note-table">
                        <tr>
                            <td>-IJAZAH PERWIRA</td>
                            <td>:</td>
                            <td>6</td>
                            <td>ORANG</td>
                            <td>-RATING ABK</td>
                            <td>:</td>
                            <td>7</td>
                            <td>ORANG</td>
                        </tr>
                        <tr>
                            <td>-ENDORSEMENT</td>
                            <td>:</td>
                            <td>6</td>
                            <td>ORANG</td>
                            <td>-BUKU PELAUT</td>
                            <td>:</td>
                            <td>11</td>
                            <td>ORANG</td>
                        </tr>
                        <tr>
                            <td>-BST</td>
                            <td>:</td>
                            <td>11</td>
                            <td>ORANG</td>
                            <td>-STAMPLE</td>
                            <td>:</td>
                            <td>1</td>
                            <td>BUAH</td>
                        </tr>
                    </table>
                </div>

                <p class="confirmation-text">
                    Telah di terima dokumen - dokumen yang tercantum diatas dengan baik dan cukup yang ditanda tangani dibawah ini.
                </p>

                <div class="signatures">
                    <table class="signature-table">
                        <tr>
                            <td class="signature-date" colspan="3">
                                BOOM BARU, PALEMBANG <?php echo e(\Carbon\Carbon::now()->translatedFormat('d F Y')); ?>

                            </td>
                        </tr>
                        <tr>
                            <td>DITERIMA</td>
                            <td>DIPERIKSA</td>
                            <td>DIKETAHUI</td>
                        </tr>
                        <tr>
                            <td class="signature-space">
                                <div class="signature-container">
                                    <div class="name">M. YUSUF</div>
                                    <div class="title">NAKHODA TB. GEMA 201/BG. GEMA 301</div>
                                </div>
                            </td>
                            <td class="signature-space">
                                <div class="signature-container">
                                    <img src="<?php echo e(public_path('images/ttd-periksa.png')); ?>" style="width: 100px; height: 100px; object-fit: contain; margin: 5px 0;">
                                    <div class="name">PANGESTU PARAMA SHANDANI</div>
                                    <div class="title">OPERATION MANAGER</div>
                                </div>
                            </td>
                            <td class="signature-space">
                                <div class="signature-container">
                                    <div class="name">HERLINA SARI</div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html> <?php /**PATH C:\laragon\www\sikapal\resources\views/dokumen/pdf/list.blade.php ENDPATH**/ ?>