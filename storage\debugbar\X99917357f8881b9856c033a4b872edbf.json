{"__meta": {"id": "X99917357f8881b9856c033a4b872edbf", "datetime": "2025-07-20 08:42:24", "utime": **********.790734, "method": "GET", "uri": "/dokumen-kapal/view/yLBXq2DWAd", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[08:42:24] LOG.info: Mencari kapal dengan ID: 5", "message_html": null, "is_string": false, "label": "info", "time": **********.734395, "xdebug_link": null, "collector": "log"}, {"message": "[08:42:24] LOG.info: <PERSON><PERSON> di<PERSON>n {\n    \"kapal\": {\n        \"id\": 5,\n        \"nama\": \"BG. GEMA 301\",\n        \"rh_me_ps\": null,\n        \"rh_me_sb\": null,\n        \"jenis_kapal_id\": 2,\n        \"created_at\": \"2024-12-16T09:41:55.000000Z\",\n        \"updated_at\": \"2024-12-20T06:16:06.000000Z\",\n        \"gt_nt\": \"3126\\/938\",\n        \"port_of_registry\": \"TANJUNG PRIOK\",\n        \"tanda_pendaftaran\": \"2013 Ba No. 3361\\/L\",\n        \"call_sign\": null,\n        \"imo\": null,\n        \"mesin\": null,\n        \"tanda_selar\": null\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.73727, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.447744, "end": **********.790765, "duration": 0.3430211544036865, "duration_str": "343ms", "measures": [{"label": "Booting", "start": **********.447744, "relative_start": 0, "end": **********.654193, "relative_end": **********.654193, "duration": 0.20644903182983398, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.654206, "relative_start": 0.20646214485168457, "end": **********.790769, "relative_end": 4.0531158447265625e-06, "duration": 0.13656306266784668, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27426680, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "dokumen.show", "param_count": null, "params": [], "start": **********.750064, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.phpdokumen.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.783836, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.784787, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.786153, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dokumen-kapal/view/{kapal}", "middleware": "web, auth, Closure", "controller": "App\\Http\\Controllers\\DokumenKapalController@show", "namespace": null, "prefix": "/dokumen-kapal", "where": [], "as": "dokumen.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=54\" onclick=\"\">app/Http/Controllers/DokumenKapalController.php:54-70</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02381, "accumulated_duration_str": "23.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.698273, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.702331, "duration": 0.0228, "duration_str": "22.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 95.758}, {"sql": "select * from `kapal` where `kapal`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.735184, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:63", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=63", "ajax": false, "filename": "DokumenKapalController.php", "line": "63"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.758, "width_percent": 2.016}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 5 and `dokumen_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 238}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.756843, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "dokumen.show:238", "source": {"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=238", "ajax": false, "filename": "show.blade.php", "line": "238"}, "connection": "gema_kapal", "explain": null, "start_percent": 97.774, "width_percent": 2.226}]}, "models": {"data": {"App\\Models\\DokumenKapal": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd\"\n]"}, "request": {"path_info": "/dokumen-kapal/view/yLBXq2DWAd", "status_code": "<pre class=sf-dump id=sf-dump-704093418 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-704093418\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1455220256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1455220256\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1453428315 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1453428315\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1930403861 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImI3QUVJSUVPWWhNWHJCZGNCNU5kZFE9PSIsInZhbHVlIjoiMHlKYXZRemQrQXFQSEwzVVRoazhpVnlta3pGdUVMWldOemhaZG9tanFlbmlMMm9kbkRaTDl0aVN5VlB4bnZreERxR0pVaGJmTkh3MzEyRlJtdWlTbTMxTzExOTRtc3RJWHBUaEM0N3NYUkl2Z3ZXUWZKWGphcUZRejhoK3BoUm0iLCJtYWMiOiIyNjE5Y2MzODRmYmU3ZTYxNDA1YTZkZTFjNDM3ODA3MDNmNTAzYTdiYWQxMzU5ZTZhMTRmODE4NGY2NDAzZmEwIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IkxFQlcySnV1MmFFayt2UE9hQVllRXc9PSIsInZhbHVlIjoiWWZsTE5HeERLQ2tQaG5kM2U1d1RuREg4MFNFTUgxaDVrK0ZVSkZjL2hSelJITkFBc1E5WS9taHJFeHd3c2dNTUNOUjA5V2JMUkJiQ0xaU3NqcnhZaGovbTJKRVkveUN4ZCtiTklTUTlWckNGNWQ3cmdlcXFyQXNCQldBeHlEVWgiLCJtYWMiOiI4YjNhZjExOWVmMGVmOTJiYThkNTQwMTg0YmQ3ZGU1YTQ5ZTk4NzA1MWIwNWEzZDZkODY3Y2FiZTY5NjkxZmVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930403861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-284848564 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z47ckW1sW3vByhJlsabzz4Jt9SLwE6fzIeTpobGt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284848564\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-821712191 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:42:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpxSnh5Ky92T25ManJ5Z1kvVjVqd1E9PSIsInZhbHVlIjoiM2xMWElYYlBWNjNTRE1RVWlLdk1kT3hzLzcybVhEMnVDWUsrbmpNaGN6OFJGL1JZWEwzS0RhdDJ5Uks1N0dOQUR0UkJzZm94M3E4Ris4TzFOLzYvM0U5MVN2dk5vZkQrcHRxMWpLa0N1d0IrV2s2Y3NlWkNoQU83aTBRV2Z1Z2wiLCJtYWMiOiIzNDAzZGRkNDM2OGUyMjM4MmNlMDFiODIyMjY1OWU1NWE0NWE2M2YyZmI5NjBlMzYxZDRlYWQ4MjFiMmY4YjI2IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IndkT2p1bHNORFNWNHFVQVh0bm5xUHc9PSIsInZhbHVlIjoicnBaUW1rN0UzRkh6a2RXN3NLcXMrWjgvWWhobER6YTVWSHE2R0M1V3hyWC9PVjNiSEViTU9Lcm9tNW9aNWphWndCdndVZmtFV2tLZFBoM1N4TlZVSDlvclY3SnZnQnBBOTA2NWdIeTc4eGw2TFdJckJlUms2Ri9JMXl6VUkwNlUiLCJtYWMiOiJhZGVjY2MyYjFkN2YzYzhhNzQyYTFjNzlhM2JiYTgwOTI4ZjBiZGEzMmJiNDM0OTMwMDRhNzI5NDQzMGVjYzNmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpxSnh5Ky92T25ManJ5Z1kvVjVqd1E9PSIsInZhbHVlIjoiM2xMWElYYlBWNjNTRE1RVWlLdk1kT3hzLzcybVhEMnVDWUsrbmpNaGN6OFJGL1JZWEwzS0RhdDJ5Uks1N0dOQUR0UkJzZm94M3E4Ris4TzFOLzYvM0U5MVN2dk5vZkQrcHRxMWpLa0N1d0IrV2s2Y3NlWkNoQU83aTBRV2Z1Z2wiLCJtYWMiOiIzNDAzZGRkNDM2OGUyMjM4MmNlMDFiODIyMjY1OWU1NWE0NWE2M2YyZmI5NjBlMzYxZDRlYWQ4MjFiMmY4YjI2IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IndkT2p1bHNORFNWNHFVQVh0bm5xUHc9PSIsInZhbHVlIjoicnBaUW1rN0UzRkh6a2RXN3NLcXMrWjgvWWhobER6YTVWSHE2R0M1V3hyWC9PVjNiSEViTU9Lcm9tNW9aNWphWndCdndVZmtFV2tLZFBoM1N4TlZVSDlvclY3SnZnQnBBOTA2NWdIeTc4eGw2TFdJckJlUms2Ri9JMXl6VUkwNlUiLCJtYWMiOiJhZGVjY2MyYjFkN2YzYzhhNzQyYTFjNzlhM2JiYTgwOTI4ZjBiZGEzMmJiNDM0OTMwMDRhNzI5NDQzMGVjYzNmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821712191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1379442887 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1379442887\", {\"maxDepth\":0})</script>\n"}}