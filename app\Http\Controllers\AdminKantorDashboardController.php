<?php

namespace App\Http\Controllers;

use App\Models\PembelianKantor;
use App\Models\PengeluaranKantor;
use App\Models\Kapal;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminKantorDashboardController extends Controller
{
    public function index()
    {
        // Total Pembelian
        $totalPembelian = PembelianKantor::count();
        
        // Total Pengeluaran
        $totalPengeluaran = PengeluaranKantor::count();
        
        // Transaksi Hari Ini (Pembelian + Pengeluaran)
        $today = Carbon::today();
        $transaksiHariIni = PembelianKantor::whereDate('created_at', $today)->count() 
                         + PengeluaranKantor::whereDate('created_at', $today)->count();
        
        // Persentase perubahan
        $yesterday = Carbon::yesterday();
        
        // Persentase Pembelian
        $pembelianKemarin = PembelianKantor::whereDate('created_at', $yesterday)->count();
        $persentasePembelian = $pembelianKemarin > 0 
            ? (($totalPembelian - $pembelianKemarin) / $pembelianKemarin) * 100 
            : 100;
        
        // Persentase Pengeluaran
        $pengeluaranKemarin = PengeluaranKantor::whereDate('created_at', $yesterday)->count();
        $persentasePengeluaran = $pengeluaranKemarin > 0 
            ? (($totalPengeluaran - $pengeluaranKemarin) / $pengeluaranKemarin) * 100 
            : 100;
        
        // Persentase Transaksi
        $transaksiKemarin = PembelianKantor::whereDate('created_at', $yesterday)->count() 
                         + PengeluaranKantor::whereDate('created_at', $yesterday)->count();
        $persentaseTransaksi = $transaksiKemarin > 0 
            ? (($transaksiHariIni - $transaksiKemarin) / $transaksiKemarin) * 100 
            : 100;

        // Hitung total sparepart
        $totalSparepart = DB::table('data_sparepart')->count();
        
        // Hitung persentase perubahan sparepart
        $sparepartBulanLalu = DB::table('data_sparepart')
            ->whereMonth('created_at', '=', now()->subMonth()->month)
            ->count();
        
        $persentaseSparepart = $sparepartBulanLalu > 0 
            ? (($totalSparepart - $sparepartBulanLalu) / $sparepartBulanLalu) * 100 
            : 100;

        // Hitung pembelian baru (14 hari terakhir)
        $pembelianBaru = PembelianKantor::where('created_at', '>=', now()->subDays(14))->count();

        // Hitung pengeluaran baru (14 hari terakhir) 
        $pengeluaranBaru = PengeluaranKantor::where('created_at', '>=', now()->subDays(14))->count();

        // Ambil data stok yang kosong atau hampir habis
        $stokKosong = DB::table('data_sparepart as ds')
            ->select(
                'ds.id',
                'ds.nama_barang',
                'ds.nomor_seri',
                'ds.jenis',
                'ds.satuan',
                DB::raw('(
                    SELECT COALESCE(SUM(CASE 
                        WHEN jenis_transaksi = "penerimaan_sparepart_kapal" THEN jumlah 
                        WHEN jenis_transaksi = "pemakaian_sparepart_kapal" THEN -jumlah 
                        ELSE 0 
                    END), 0)
                    FROM riwayat_stok_kantor 
                    WHERE id_barang = ds.id 
                    AND jenis_transaksi IN ("penerimaan_sparepart_kapal", "pemakaian_sparepart_kapal")
                ) as stok_tersedia'),
                DB::raw('(
                    SELECT GROUP_CONCAT(DISTINCT pk.kapal_id) 
                    FROM pengeluaran_kantor pk
                    JOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian
                    WHERE pmk.id_barang = ds.id
                    AND pk.jumlah > 0
                ) as kapal_dengan_stok')
            )
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                      ->from('riwayat_stok_kantor')
                      ->whereRaw('riwayat_stok_kantor.id_barang = ds.id')
                      ->whereIn('jenis_transaksi', ['penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal']);
            })
            ->having('stok_tersedia', '<=', 0)
            ->having('kapal_dengan_stok', '=', null)
            ->get();

        $jumlahStokKosong = $stokKosong->count();

        // Cek stok kapal yang habis atau sudah didistribusikan
        $stokKapalKosong = DB::table('kapal as k')
            ->join('pengeluaran_kantor as pk', 'k.id', '=', 'pk.kapal_id')
            ->join('pembelian_kantor as pmk', 'pmk.id', '=', 'pk.id_pembelian')
            ->join('data_sparepart as ds', 'pmk.id_barang', '=', 'ds.id')
            ->leftJoin('penerimaan_kapal as prk', function($join) {
                $join->on('k.id', '=', 'prk.kapal_id')
                    ->on('pk.id', '=', 'prk.pengeluaran_kantor_id');
            })
            ->select(
                'k.id as kapal_id',
                'k.nama as nama_kapal',
                'k.jenis_kapal_id',
                'ds.id as id_barang',
                'ds.nama_barang',
                'ds.nomor_seri',
                'ds.jenis',
                'ds.satuan',
                DB::raw('COALESCE(SUM(prk.jumlah), 0) as stok_tersedia'),
                DB::raw('MAX(pk.jumlah) as jumlah_pengeluaran')
            )
            ->whereExists(function($query) {
                $query->select(DB::raw(1))
                    ->from('pengeluaran_kantor')
                    ->whereColumn('pengeluaran_kantor.kapal_id', 'k.id')
                    ->join('pembelian_kantor', 'pembelian_kantor.id', '=', 'pengeluaran_kantor.id_pembelian')
                    ->whereColumn('pembelian_kantor.id_barang', 'ds.id');
            })
            ->groupBy(
                'k.id',
                'k.nama',
                'k.jenis_kapal_id',
                'ds.id',
                'ds.nama_barang',
                'ds.nomor_seri',
                'ds.jenis',
                'ds.satuan'
            )
            ->having('stok_tersedia', '=', 0)
            ->get();

        // Debug untuk melihat hasil query
        \Log::info('Stok Kapal Kosong:', [
            'query' => DB::getQueryLog(),
            'result' => $stokKapalKosong
        ]);

        $jumlahKapalStokKosong = $stokKapalKosong->count();

        return view('admin_kantor.dashboard', compact(
            'totalPembelian',
            'totalPengeluaran',
            'transaksiHariIni',
            'persentasePembelian',
            'persentasePengeluaran',
            'persentaseTransaksi',
            'totalSparepart',
            'persentaseSparepart',
            'pembelianBaru',
            'pengeluaranBaru',
            'stokKosong',
            'jumlahStokKosong',
            'stokKapalKosong',
            'jumlahKapalStokKosong'
        ));
    }

    /**
     * Menampilkan daftar kapal tugboat dan tongkang untuk admin kantor
     */
    public function daftarKapal()
    {
        // Ambil semua kapal tugboat dan tongkang
        $tugboats = Kapal::with(['jenis_kapal', 'dokumen'])
            ->where('jenis_kapal_id', 1) // Tugboat
            ->get();

        $tongkangs = Kapal::with(['jenis_kapal', 'dokumen'])
            ->where('jenis_kapal_id', 2) // Tongkang
            ->get();

        // Hitung statistik dokumen untuk setiap kapal
        $tugboats = $tugboats->map(function ($kapal) {
            $kapal->total_dokumen = $kapal->dokumen->count();
            $kapal->dokumen_berlaku = $kapal->dokumen->where('keterangan', 'Berlaku')->count();
            $kapal->dokumen_tidak_berlaku = $kapal->dokumen->where('keterangan', 'Tidak Berlaku')->count();
            $kapal->dokumen_belum_diatur = $kapal->dokumen->where('keterangan', 'Belum diatur')->count();
            return $kapal;
        });

        $tongkangs = $tongkangs->map(function ($kapal) {
            $kapal->total_dokumen = $kapal->dokumen->count();
            $kapal->dokumen_berlaku = $kapal->dokumen->where('keterangan', 'Berlaku')->count();
            $kapal->dokumen_tidak_berlaku = $kapal->dokumen->where('keterangan', 'Tidak Berlaku')->count();
            $kapal->dokumen_belum_diatur = $kapal->dokumen->where('keterangan', 'Belum diatur')->count();
            return $kapal;
        });

        return view('admin_kantor.kapal.index', compact('tugboats', 'tongkangs'));
    }
}