{"__meta": {"id": "X7f4c898ede3c884c62dde474313bee1d", "datetime": "2025-07-20 08:09:07", "utime": **********.216528, "method": "POST", "uri": "/checklist-engine/store-item", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752973746.708639, "end": **********.216552, "duration": 0.5079131126403809, "duration_str": "508ms", "measures": [{"label": "Booting", "start": 1752973746.708639, "relative_start": 0, "end": **********.001455, "relative_end": **********.001455, "duration": 0.292816162109375, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.00147, "relative_start": 0.2928311824798584, "end": **********.216555, "relative_end": 3.0994415283203125e-06, "duration": 0.21508502960205078, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 26119832, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST checklist-engine/store-item", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\ChecklistEngineController@storeItemPemeriksaan", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.store-item", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1658\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:1658-1713</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0205, "accumulated_duration_str": "20.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.061829, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.067235, "duration": 0.01822, "duration_str": "18.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 88.878}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1676}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.106346, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:1676", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1676}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1676", "ajax": false, "filename": "ChecklistEngineController.php", "line": "1676"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.878, "width_percent": 2.732}, {"sql": "insert into `hasil_pemeriksaan_engine` (`kapal_id`, `history_id`, `item_id`, `kondisi`, `keterangan`, `tanggal_pemeriksaan`, `created_at`, `updated_at`) values (4, 24, '174', 'bagus', '-', '2025-07-20T08:07', '2025-07-20 08:09:07', '2025-07-20 08:09:07')", "type": "query", "params": [], "bindings": [4, 24, "174", "bagus", "-", "2025-07-20T08:07", "2025-07-20 08:09:07", "2025-07-20 08:09:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1694}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.10832, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:1694", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 1694}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=1694", "ajax": false, "filename": "ChecklistEngineController.php", "line": "1694"}, "connection": "gema_kapal", "explain": null, "start_percent": 91.61, "width_percent": 8.39}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/store-item", "status_code": "<pre class=sf-dump id=sf-dump-1999331419 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1999331419\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1808944336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1808944336\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1820895325 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>item_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">174</span>\"\n  \"<span class=sf-dump-key>engine</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SITUASIONAL</span>\"\n  \"<span class=sf-dump-key>kapal_id</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2NaDK50q4p</span>\"\n  \"<span class=sf-dump-key>kondisi</span>\" => \"<span class=sf-dump-str title=\"5 characters\">bagus</span>\"\n  \"<span class=sf-dump-key>keterangan</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tanggal_pemeriksaan</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-07-20T08:07</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820895325\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1180950503 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">801</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryvRXRfEmMTMU8qWuB</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjNTTllxeEdZNXZwTVA4cllqNWlDSVE9PSIsInZhbHVlIjoicFNFMFZjS1pZNVZybENuZHYzb2VISlB1SVNBNCtUTmpPU1JybzN3aXZ3T20rRlZQdlF2NGpPZ1VBRmZ2U1pLZ2dhVmNNejNXaldFajlscnJia2RLbjBweWxrVS8xd3BVa2RJWm8weVlIZVVKcE96enVpdWhWMXZjQlo4OFBXMXYiLCJtYWMiOiI1N2IwZDAzNzU0YTljYzJjMDFkMjFjY2Q5ZTBkMmFkZTNjMjljOGVjNmZiMTAxZDUzZmQ4YzAzMGVmM2IxNDhmIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImNBMC9DVmxVbGozM05wOHdzdmNqdnc9PSIsInZhbHVlIjoid2lUMnBQbTlnMXQ5VGJ4SDZyOXp6L2h0OCsxM25uR0RkdmQxbTBiVHkvYmRsUm9HbzR3VSsvRXB3c3B6NlZUakFnUDRMQThDaGhncitveGY4aUhEa3MwekQ3YnphNXZ0eDhsRko4TUJmeXVIL2dEK0N2eGlaRi9NVmpZMWdkSzAiLCJtYWMiOiIwMjc1MjRjZDQ4NDE2ODYxYzcxZTdjMzYxYjc3OThjMmNkN2M0YjgzYTUzZTBhZWM5NmMxYjkyODE4MGVjMTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180950503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-735518186 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:09:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImNrVVc0VXV0cjJvellMK25PUWp2dnc9PSIsInZhbHVlIjoibkVTeGVhOHlOUkx6SXVheUthZTlReGhSTkpJeUFLWmc2UUp6QlZTUEY2WVYxWGxBS2F1RUN5SGxGS3J2K2tIdDlwMzMxeS9XVFk2ZTl5NUJyMGhxUHJQSk5CanFSSmxNbWRjNzBkZi80aitBQVFFRGhEWDFYVXI3S2FLbXhXajEiLCJtYWMiOiJkMTRhOGE1MmJmYzNkMjg1NjI4NzZjMTU5NjdlMGM1NGE0NWE2NTY5NzMxYjI1YzRjNWE4NDc1OWM3YjY4MjEwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6InBuQlgxRnpaTlcvR3Awb0duZjBpOGc9PSIsInZhbHVlIjoiRjhQWHlOMjZBZjY5ejBKcGhxc0dtTzlEcGt3UzhCWUlXMnZkQkowQU5WQ1dFZ0RYRXduU2JYZnlzVjdqTzg1QWliVnlSZHhUR1pKRUU4c0RGcmdIaDRORGl1QlVtUlJVb29vUk5GTjVEN3RNTTBIYzBoR1hFZnhRNXNDd1pWVVUiLCJtYWMiOiJlMzA4MTMwOWU5ZjI2ZDhkZjZhZGVkZmMzNWNlMWFhNjk2YTdkYWRhNGNhNjllZTQ3MDliZjZhOTYxMmJmM2U5IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImNrVVc0VXV0cjJvellMK25PUWp2dnc9PSIsInZhbHVlIjoibkVTeGVhOHlOUkx6SXVheUthZTlReGhSTkpJeUFLWmc2UUp6QlZTUEY2WVYxWGxBS2F1RUN5SGxGS3J2K2tIdDlwMzMxeS9XVFk2ZTl5NUJyMGhxUHJQSk5CanFSSmxNbWRjNzBkZi80aitBQVFFRGhEWDFYVXI3S2FLbXhXajEiLCJtYWMiOiJkMTRhOGE1MmJmYzNkMjg1NjI4NzZjMTU5NjdlMGM1NGE0NWE2NTY5NzMxYjI1YzRjNWE4NDc1OWM3YjY4MjEwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6InBuQlgxRnpaTlcvR3Awb0duZjBpOGc9PSIsInZhbHVlIjoiRjhQWHlOMjZBZjY5ejBKcGhxc0dtTzlEcGt3UzhCWUlXMnZkQkowQU5WQ1dFZ0RYRXduU2JYZnlzVjdqTzg1QWliVnlSZHhUR1pKRUU4c0RGcmdIaDRORGl1QlVtUlJVb29vUk5GTjVEN3RNTTBIYzBoR1hFZnhRNXNDd1pWVVUiLCJtYWMiOiJlMzA4MTMwOWU5ZjI2ZDhkZjZhZGVkZmMzNWNlMWFhNjk2YTdkYWRhNGNhNjllZTQ3MDliZjZhOTYxMmJmM2U5IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735518186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-394552255 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394552255\", {\"maxDepth\":0})</script>\n"}}