<table class="documents">
    <thead>
        <tr>
            <th rowspan="2" class="no-column">NO</th>
            <th class="sertifikat">SERTIFIKAT KAPAL MEMENUHI</th>
            <th rowspan="2" class="diberikan-column">DIBERIKAN DI</th>
            <th rowspan="2" class="tanggal-column">TANGGAL</th>
            <th rowspan="2" class="berlaku-column">BERLAKU S/D</th>
            <th rowspan="2" class="ket-column">KET</th>
        </tr>
        <tr>
            <th class="sertifikat">KETENTUAN DAN MASIH BERLAKU</th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $dokumen; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $dok): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr <?php if($dok->berlaku_sampai && $dok->berlaku_sampai->isPast()): ?> class="expired" <?php endif; ?>>
                <td><?php echo e($index + 1); ?></td>
                <td class="sertifikat-column"><?php echo e($dok->sertifikat_status ?: $dok->nama_file); ?></td>
                <td><?php echo e(strtoupper($dok->diberikan_di ?: '-')); ?></td>
                <td><?php echo e($dok->tanggal_diberikan ? $dok->tanggal_diberikan->format('d F Y') : '-'); ?></td>
                <td <?php if($dok->berlaku_selamanya): ?> class="green-text" <?php endif; ?>>
                    <?php if($dok->berlaku_selamanya): ?>
                        SELAMANYA
                    <?php else: ?>
                        <?php echo e($dok->berlaku_sampai ? $dok->berlaku_sampai->format('d F Y') : '-'); ?>

                        <?php if($dok->is_endorsed && $dok->tanggal_endorse): ?>
                            <br>TGL ENDORSE <?php echo e($dok->tanggal_endorse->format('d F Y')); ?>

                        <?php endif; ?>
                    <?php endif; ?>
                </td>
                <td><?php echo e($dok->keterangan); ?></td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>

<div class="table-footer-space"></div> <?php /**PATH C:\laragon\www\sikapal\resources\views/dokumen/pdf/partials/dokumen-table.blade.php ENDPATH**/ ?>