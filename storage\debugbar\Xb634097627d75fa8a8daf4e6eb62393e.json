{"__meta": {"id": "Xb634097627d75fa8a8daf4e6eb62393e", "datetime": "2025-07-20 08:27:21", "utime": **********.486157, "method": "GET", "uri": "/admin/kantor/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[08:27:21] LOG.info: Stok Kapal Kosong: {\n    \"query\": [],\n    \"result\": [\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 225,\n            \"nama_barang\": \"FUEL FILTER SCREEN\",\n            \"nomor_seri\": \"FUEL FILTER SCREEN\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 226,\n            \"nama_barang\": \"PRESSURE TRANSMITER\",\n            \"nomor_seri\": \"04541 - 90200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 227,\n            \"nama_barang\": \"GASKET CYL HEAD (TSP)\",\n            \"nomor_seri\": \"3750112200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 228,\n            \"nama_barang\": \"PACKING, ROCKER CASE\",\n            \"nomor_seri\": \"3750441200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 229,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"3750402300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 230,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550710200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 231,\n            \"nama_barang\": \"SEAL, VALVE STEM\",\n            \"nomor_seri\": \"3750400900\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 232,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550531065\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 233,\n            \"nama_barang\": \"ORING LINER\",\n            \"nomor_seri\": \"3750732400\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 234,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704201\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 235,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.460978, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.142248, "end": **********.486181, "duration": 0.34393310546875, "duration_str": "344ms", "measures": [{"label": "Booting", "start": **********.142248, "relative_start": 0, "end": **********.361223, "relative_end": **********.361223, "duration": 0.21897506713867188, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.361237, "relative_start": 0.21898913383483887, "end": **********.486184, "relative_end": 2.86102294921875e-06, "duration": 0.12494683265686035, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25713952, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.dashboard", "param_count": null, "params": [], "start": **********.475789, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/dashboard.blade.phpadmin_kantor.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.47941, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.480611, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.482287, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/dashboard", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=13\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:13-169</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026760000000000003, "accumulated_duration_str": "26.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.403034, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.407448, "duration": 0.016300000000000002, "duration_str": "16.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 60.912}, {"sql": "select count(*) as aggregate from `pembelian_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.432053, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:16", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=16", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "16"}, "connection": "gema_kapal", "explain": null, "start_percent": 60.912, "width_percent": 7.698}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.436617, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:19", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=19", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "19"}, "connection": "gema_kapal", "explain": null, "start_percent": 68.61, "width_percent": 6.876}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.439923, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:23", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=23", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 75.486, "width_percent": 1.495}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.441335, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:24", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=24", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 76.981, "width_percent": 0.785}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4425879, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:30", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=30", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "30"}, "connection": "gema_kapal", "explain": null, "start_percent": 77.765, "width_percent": 1.607}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.444047, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:36", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=36", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "36"}, "connection": "gema_kapal", "explain": null, "start_percent": 79.372, "width_percent": 0.598}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.445146, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=42", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "42"}, "connection": "gema_kapal", "explain": null, "start_percent": 79.97, "width_percent": 1.308}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.446458, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:43", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=43", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "43"}, "connection": "gema_kapal", "explain": null, "start_percent": 81.278, "width_percent": 0.598}, {"sql": "select count(*) as aggregate from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4476328, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:49", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=49", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "49"}, "connection": "gema_kapal", "explain": null, "start_percent": 81.876, "width_percent": 5.531}, {"sql": "select count(*) as aggregate from `data_sparepart` where month(`created_at`) = '06'", "type": "query", "params": [], "bindings": ["06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.450224, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:54", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=54", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "54"}, "connection": "gema_kapal", "explain": null, "start_percent": 87.407, "width_percent": 0.822}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where `created_at` >= '2025-07-06 08:27:21'", "type": "query", "params": [], "bindings": ["2025-07-06 08:27:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.45147, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=61", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "61"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.229, "width_percent": 0.934}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where `created_at` >= '2025-07-06 08:27:21'", "type": "query", "params": [], "bindings": ["2025-07-06 08:27:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.452703, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:64", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=64", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "64"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.163, "width_percent": 0.673}, {"sql": "select `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, (\nSELECT COALESCE(SUM(CASE\nWHEN jenis_transaksi = \"penerimaan_sparepart_kapal\" THEN jumlah\nWHEN jenis_transaksi = \"pemakaian_sparepart_kapal\" THEN -jumlah\nELSE 0\nEND), 0)\nFROM riwayat_stok_kantor\nWHERE id_barang = ds.id\nAND jenis_transaksi IN (\"penerimaan_sparepart_kapal\", \"pemakaian_sparepart_kapal\")\n) as stok_tersedia, (\nSELECT GROUP_CONCAT(DISTINCT pk.kapal_id)\nFROM pengeluaran_kantor pk\nJOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian\nWHERE pmk.id_barang = ds.id\nAND pk.jumlah > 0\n) as kapal_dengan_stok from `data_sparepart` as `ds` where exists (select 1 from `riwayat_stok_kantor` where riwayat_stok_kantor.id_barang = ds.id and `jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal')) having `stok_tersedia` <= 0 and `kapal_dengan_stok` = null", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", 0, null], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 100}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.454115, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:100", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=100", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "100"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.836, "width_percent": 6.054}, {"sql": "select `k`.`id` as `kapal_id`, `k`.`nama` as `nama_kapal`, `k`.`jenis_kapal_id`, `ds`.`id` as `id_barang`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, COALESCE(SUM(prk.jumlah), 0) as stok_tersedia, MAX(pk.jumlah) as jumlah_pengeluaran from `kapal` as `k` inner join `pengeluaran_kantor` as `pk` on `k`.`id` = `pk`.`kapal_id` inner join `pembelian_kantor` as `pmk` on `pmk`.`id` = `pk`.`id_pembelian` inner join `data_sparepart` as `ds` on `pmk`.`id_barang` = `ds`.`id` left join `penerimaan_kapal` as `prk` on `k`.`id` = `prk`.`kapal_id` and `pk`.`id` = `prk`.`pengeluaran_kantor_id` where exists (select 1 from `pengeluaran_kantor` inner join `pembelian_kantor` on `pembelian_kantor`.`id` = `pengeluaran_kantor`.`id_pembelian` where `pengeluaran_kantor`.`kapal_id` = `k`.`id` and `pembelian_kantor`.`id_barang` = `ds`.`id`) group by `k`.`id`, `k`.`nama`, `k`.`jenis_kapal_id`, `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan` having `stok_tersedia` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.457206, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:143", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=143", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "143"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.889, "width_percent": 4.111}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/dashboard\"\n]"}, "request": {"path_info": "/admin/kantor/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-398687830 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-398687830\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-64677312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-64677312\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-440791834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-440791834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1757268662 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/2NaDK50q4p</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ikd1c0ZzVjdtV0xiYTFvWGVSbnUyaXc9PSIsInZhbHVlIjoia3ZSekVTNFdWc0l6VmM3NmUrMXpaTFp5U1dwU04xeDdwWmhEZWl0QzFuUGNpbm5HdWVEb3RZVjl0RVJ6eTFlMGZyaGt6c1JHRXRrU0c0eWZqOFFBTDlvOFplMkw0bzlHWkdGUjBuRE9JbjhmbkhKenR5Q2VrQzF2WkZCUGI3a04iLCJtYWMiOiJiNmZhNmIwZWQzYjE3NTQ5NzI4ZTRhNTExNjBhYzE2OWMzZWYxYWUzZTBjY2VjZmVkNGU2ZDJlODA0ZmRlMjQ4IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Ik82b1N5K1lkREROaWNxTWlyTUJqMHc9PSIsInZhbHVlIjoiYmZ0VjAyRWR6NzMyUzJiNVlsQk1yVXhLUlo2RFJDb2puMnBaUDI4T0xyR0lLb0JhZDFhQWVUNjMxWTZkL2I5QnE1S3lpSnFRbC8rR29PUTc1T2JhcVFJWDhIV3pna0R1dWhXSG5aMGlGaXI1RTV4c3lwakhVMldncE9WQURSNW0iLCJtYWMiOiJkMDg5NmIwMzMxZTQxMjRiZmIyZWI0MmUwYTg3ZmM4NmFkYmVlZjQ4MDJkNzE0NGYxZTRjZjFiMTljYWU3YmFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757268662\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-952464332 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952464332\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-472457330 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZiRlNSWU1HRlZPU09PVVZLcjRoVlE9PSIsInZhbHVlIjoicWlHbVBNUm9yUkNtZHFhamVBWFVBZVdTN2pwM0pCMnI3dDdlTFRZL0JRWkk4RGdzY2htc3NZdFBUQjZrWk1nTjNaT2lEc3paMHhhcWNWV0Z2NGpVUnRESmJKQlZhUzZwNXp4ZTd3OTdvaUZaaDU4UGMwZ0RLcjk4UjFSSGdabVkiLCJtYWMiOiJiNjM1ZDMzZjU2MmYwMGIyODc1ODVhNWM4ODJmYmE2ODcyZjIzYTBlYjM0NmQwMTY5ZTRiYzI1MzZmZjlkOGM0IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IjRqTEdZSloxbTBpamxwOUJDTURMYkE9PSIsInZhbHVlIjoid05pYVg3WS92VW9TRlZtRFpTZzEvbk1sY09PYnJzUTlPZVdRaWNuYi90VHdpYzVWS1NYNTZ6WTZzdzY1dmhqMzczK2N5WFlXK3Urc29KYUlKdmJ0SjZkN2w4RDRxRzFISU5RTGViSzFWNEIzZWlKcU9ITGdjaTRVdkswNWNFdFAiLCJtYWMiOiI4N2EzOTQxNTNmNTI3ZmQzMmUzYzg5ZTAwMjIxM2Y0MTlkNDg1NjJjMDY4OTc3YTczMmZlMjk1ODdhNTZmMjFlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZiRlNSWU1HRlZPU09PVVZLcjRoVlE9PSIsInZhbHVlIjoicWlHbVBNUm9yUkNtZHFhamVBWFVBZVdTN2pwM0pCMnI3dDdlTFRZL0JRWkk4RGdzY2htc3NZdFBUQjZrWk1nTjNaT2lEc3paMHhhcWNWV0Z2NGpVUnRESmJKQlZhUzZwNXp4ZTd3OTdvaUZaaDU4UGMwZ0RLcjk4UjFSSGdabVkiLCJtYWMiOiJiNjM1ZDMzZjU2MmYwMGIyODc1ODVhNWM4ODJmYmE2ODcyZjIzYTBlYjM0NmQwMTY5ZTRiYzI1MzZmZjlkOGM0IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IjRqTEdZSloxbTBpamxwOUJDTURMYkE9PSIsInZhbHVlIjoid05pYVg3WS92VW9TRlZtRFpTZzEvbk1sY09PYnJzUTlPZVdRaWNuYi90VHdpYzVWS1NYNTZ6WTZzdzY1dmhqMzczK2N5WFlXK3Urc29KYUlKdmJ0SjZkN2w4RDRxRzFISU5RTGViSzFWNEIzZWlKcU9ITGdjaTRVdkswNWNFdFAiLCJtYWMiOiI4N2EzOTQxNTNmNTI3ZmQzMmUzYzg5ZTAwMjIxM2Y0MTlkNDg1NjJjMDY4OTc3YTczMmZlMjk1ODdhNTZmMjFlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472457330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-175330192 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175330192\", {\"maxDepth\":0})</script>\n"}}