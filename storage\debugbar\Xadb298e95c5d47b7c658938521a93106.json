{"__meta": {"id": "Xadb298e95c5d47b7c658938521a93106", "datetime": "2025-07-20 08:26:57", "utime": **********.085197, "method": "GET", "uri": "/voyage-report/accumulated-stats?", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752974816.816873, "end": **********.085231, "duration": 0.2683579921722412, "duration_str": "268ms", "measures": [{"label": "Booting", "start": 1752974816.816873, "relative_start": 0, "end": **********.006787, "relative_end": **********.006787, "duration": 0.18991398811340332, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006805, "relative_start": 0.18993186950683594, "end": **********.085235, "relative_end": 4.0531158447265625e-06, "duration": 0.07843017578125, "duration_str": "78.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24935664, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET voyage-report/accumulated-stats", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\VoyageReportController@getAccumulatedStats", "namespace": null, "prefix": "", "where": [], "as": "voyage-report.accumulated-stats", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FVoyageReportController.php&line=376\" onclick=\"\">app/Http/Controllers/VoyageReportController.php:376-413</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01308, "accumulated_duration_str": "13.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.049644, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.053981, "duration": 0.01257, "duration_str": "12.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 96.101}, {"sql": "select\nSUM(total_waktu_berlayar_menit) as total_berlayar,\nSUM(total_waktu_berlabuh_menit) as total_berlabuh,\nSUM(total_waktu_bongkar_menit) as total_bongkar,\nSUM(konsumsi_bbm) as total_bbm,\nSUM(tonase_muatan) as total_tonase,\nCOUNT(*) as total_voyage\nfrom `voyage_reports` where `status` = 'approved' limit 1", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/VoyageReportController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\VoyageReportController.php", "line": 400}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.073949, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "VoyageReportController.php:400", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/VoyageReportController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\VoyageReportController.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FVoyageReportController.php&line=400", "ajax": false, "filename": "VoyageReportController.php", "line": "400"}, "connection": "gema_kapal", "explain": null, "start_percent": 96.101, "width_percent": 3.899}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\VoyageReport": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FVoyageReport.php&line=1", "ajax": false, "filename": "VoyageReport.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/voyage-report/accumulated-stats\"\n]"}, "request": {"path_info": "/voyage-report/accumulated-stats", "status_code": "<pre class=sf-dump id=sf-dump-1596596760 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1596596760\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-514406660 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-514406660\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-260307952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-260307952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-146831725 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://sikapal.test/voyage-report</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlkzdlNnelZPS2pEWlJvTXlLeTVub3c9PSIsInZhbHVlIjoiSjcxb09lOHE2YUc4eWNnT2ZicVIyVnFpdDQ2Vi9iWkZSalprWlpxbS8zSFp2dXNia1kxNnhFa3U5K2ZsOEIrRGlwZTJtaEpRUDRKZjhQektHbTlCMWRrMkZRdzdTWERpREsvR2JJQ1RLdHA0T3NsZFhJWSs4bkpiUVgzR0kyTEUiLCJtYWMiOiIwZTc1ZDVlY2I1NDk4MjMwM2FhMDQ3ZjZiNWU1YjA5MzJiNmM2ZDI4ZDQwYzY5ODc0YWMxNzExZjU3OWE5MDE4IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IjY1MkhQaFg5MURudVRLaWh3SHpYQ2c9PSIsInZhbHVlIjoiOE9sWk9RSnoyQWs0U1dUd2RhWU4rdU1uZlBaNTZiaWRqZGJMVkJmSEhpdkVBTFkxOU1NSk5yY3FWWExVMnlOb0VpS3VkdUtFR3BCOUdjOUJQcUI2SE5DV24rbkV4NXZZeUFEYytZZHUvbGtzWVhPWmllQ0hMaDB0QXVlaGFJMEEiLCJtYWMiOiI4NGM4MzI3NDU3MWVmOGIyYmVlNjg4YWE3MWVlZTFhNDljY2FhYjQwMTk2ODQ0Yjg1Y2Y2ODVlNTkyNTgyMWFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146831725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429453495 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429453495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-783458865 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:26:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZtL1VHTVJST3VFamQ3M1htQmJsVmc9PSIsInZhbHVlIjoidjQyRXFOV2VIcGlabXcxSjBTOGhOZFg4NmNkSzVxQWlpZ05rUkVRcUZybWw4MFkzc2ZzcjBKNkEwbFRLKzlJNWlkdHBHc1N4eThXNVZoL1I5bDlKRzA0MjRsYlV6TGQ4TXZTYXA2WmtNbDBFQXRkdkRLcUxhbUc2d2ZKREhEc2IiLCJtYWMiOiJhOWFlNjc2M2NlYTJiNTE3NTA0OGQ4YWQ3YTdkN2JjNTRiOGFlMDQyY2NmNTc2YjY5YTk5Y2I1YjUzYzg4Y2VkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:26:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ik1xQU1mcDBEVlZ5bVExTXFUS1RMUVE9PSIsInZhbHVlIjoiSHA2Mm9oMEpWM21ldlNjcExpMmVNUFR3a2g1cERLOGNCT2MxUGE5OEF6cHRRVVk0cnpZQzd5MEJHZlJmV0dwdlJYZUU2NU9sdm55aG1ydmZVMVFZT3NvbGc1dEQ4bm9mc01BeEkxS2pWc3dUaDZOcFpYMHA2SS9Vb01GVUZERjQiLCJtYWMiOiI5MzI5MmIwYjZkMzVlZjMxYmUzZDJlMWIwZDI4MTZjNTg0ZDI3OGYwY2ZlYjFhNGZmYzI4MWMyM2ViYzE3ZTkxIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:26:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZtL1VHTVJST3VFamQ3M1htQmJsVmc9PSIsInZhbHVlIjoidjQyRXFOV2VIcGlabXcxSjBTOGhOZFg4NmNkSzVxQWlpZ05rUkVRcUZybWw4MFkzc2ZzcjBKNkEwbFRLKzlJNWlkdHBHc1N4eThXNVZoL1I5bDlKRzA0MjRsYlV6TGQ4TXZTYXA2WmtNbDBFQXRkdkRLcUxhbUc2d2ZKREhEc2IiLCJtYWMiOiJhOWFlNjc2M2NlYTJiNTE3NTA0OGQ4YWQ3YTdkN2JjNTRiOGFlMDQyY2NmNTc2YjY5YTk5Y2I1YjUzYzg4Y2VkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:26:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ik1xQU1mcDBEVlZ5bVExTXFUS1RMUVE9PSIsInZhbHVlIjoiSHA2Mm9oMEpWM21ldlNjcExpMmVNUFR3a2g1cERLOGNCT2MxUGE5OEF6cHRRVVk0cnpZQzd5MEJHZlJmV0dwdlJYZUU2NU9sdm55aG1ydmZVMVFZT3NvbGc1dEQ4bm9mc01BeEkxS2pWc3dUaDZOcFpYMHA2SS9Vb01GVUZERjQiLCJtYWMiOiI5MzI5MmIwYjZkMzVlZjMxYmUzZDJlMWIwZDI4MTZjNTg0ZDI3OGYwY2ZlYjFhNGZmYzI4MWMyM2ViYzE3ZTkxIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:26:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783458865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1387861602 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">http://sikapal.test/voyage-report/accumulated-stats</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387861602\", {\"maxDepth\":0})</script>\n"}}