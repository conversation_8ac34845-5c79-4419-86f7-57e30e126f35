{"__meta": {"id": "X8f2c0bf35586f11250574b6458d825a2", "datetime": "2025-07-20 08:27:26", "utime": **********.162379, "method": "GET", "uri": "/admin/kantor/kapal", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752974845.86865, "end": **********.1624, "duration": 0.2937500476837158, "duration_str": "294ms", "measures": [{"label": "Booting", "start": 1752974845.86865, "relative_start": 0, "end": **********.063498, "relative_end": **********.063498, "duration": 0.19484806060791016, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063512, "relative_start": 0.19486212730407715, "end": **********.162404, "relative_end": 4.0531158447265625e-06, "duration": 0.0988919734954834, "duration_str": "98.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25938064, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.kapal.index", "param_count": null, "params": [], "start": **********.150407, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/kapal/index.blade.phpadmin_kantor.kapal.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fkapal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.156142, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.157123, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.158401, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/kapal", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@daftarKapal", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.kapal", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=174\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:174-203</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00476, "accumulated_duration_str": "4.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.106042, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.110014, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 50.42}, {"sql": "select * from `kapal` where `jenis_kapal_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.116318, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:179", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=179", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "179"}, "connection": "gema_kapal", "explain": null, "start_percent": 50.42, "width_percent": 13.235}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.119971, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:179", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=179", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "179"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.655, "width_percent": 7.773}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1220238, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:179", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=179", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "179"}, "connection": "gema_kapal", "explain": null, "start_percent": 71.429, "width_percent": 9.034}, {"sql": "select * from `kapal` where `jenis_kapal_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1236181, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:183", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=183", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "183"}, "connection": "gema_kapal", "explain": null, "start_percent": 80.462, "width_percent": 7.143}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.12543, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:183", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=183", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "183"}, "connection": "gema_kapal", "explain": null, "start_percent": 87.605, "width_percent": 7.143}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.126746, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:183", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=183", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "183"}, "connection": "gema_kapal", "explain": null, "start_percent": 94.748, "width_percent": 5.252}]}, "models": {"data": {"App\\Models\\DokumenKapal": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\JenisKapal": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FJenisKapal.php&line=1", "ajax": false, "filename": "JenisKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 41, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/kapal\"\n]"}, "request": {"path_info": "/admin/kantor/kapal", "status_code": "<pre class=sf-dump id=sf-dump-1483107874 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1483107874\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-258200846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-258200846\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1969613722 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1969613722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1606416252 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlZiRlNSWU1HRlZPU09PVVZLcjRoVlE9PSIsInZhbHVlIjoicWlHbVBNUm9yUkNtZHFhamVBWFVBZVdTN2pwM0pCMnI3dDdlTFRZL0JRWkk4RGdzY2htc3NZdFBUQjZrWk1nTjNaT2lEc3paMHhhcWNWV0Z2NGpVUnRESmJKQlZhUzZwNXp4ZTd3OTdvaUZaaDU4UGMwZ0RLcjk4UjFSSGdabVkiLCJtYWMiOiJiNjM1ZDMzZjU2MmYwMGIyODc1ODVhNWM4ODJmYmE2ODcyZjIzYTBlYjM0NmQwMTY5ZTRiYzI1MzZmZjlkOGM0IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IjRqTEdZSloxbTBpamxwOUJDTURMYkE9PSIsInZhbHVlIjoid05pYVg3WS92VW9TRlZtRFpTZzEvbk1sY09PYnJzUTlPZVdRaWNuYi90VHdpYzVWS1NYNTZ6WTZzdzY1dmhqMzczK2N5WFlXK3Urc29KYUlKdmJ0SjZkN2w4RDRxRzFISU5RTGViSzFWNEIzZWlKcU9ITGdjaTRVdkswNWNFdFAiLCJtYWMiOiI4N2EzOTQxNTNmNTI3ZmQzMmUzYzg5ZTAwMjIxM2Y0MTlkNDg1NjJjMDY4OTc3YTczMmZlMjk1ODdhNTZmMjFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606416252\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1022126718 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022126718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1423203899 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:27:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImtucEs2QTNEZS8xZlNVRGxwZWRJenc9PSIsInZhbHVlIjoib1dMdFpKTEZNT1BlQVR5cUF4b3RUdXlIMW9ZOE5UYnlnelB5Vk9oeVJnd0R2clZnRDNDWmdGQTI3S2xrdy9ZdmlWdlhBMG83a0JwTFBBcDNYYzN2N3lqRlVFVVRBSDBESng4K1A1QUZtMU9mTkNSZGF2elRSSzNkN3BjNzh6TFoiLCJtYWMiOiIwN2I4ZDM2NzM2ZjY1NTZlMTk0ZjViOGQ3Yzg3Yzk1YzAzNTJlZTQ3YTQ3MmVmMjE5ZmZiNzc4MDlhMmIzYWMwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6Ikh0aStJTXJsMDFSYlRRcWtJbU1rdnc9PSIsInZhbHVlIjoiZ1ZZOVVhMjRyYXNMQllscnF1eDFXd0l2eWRRMnhQdzk3MkdtTGdTaFhZWlBlR0Nxa2ZpUC9NSTN5TEZSWHhmeWZkMDdMbmppODA0Qnpzd1h5elMyQ0hlLzg4MGw2NjBid0owRzBtZkJFYThJTG1ZenEwc2lMSWVQTUpFQjNNNnAiLCJtYWMiOiIzOTQ0ZGY0NjcxNWNmNmJiZTUxY2IwYTM4ODQxNGVhM2RiYTUwYmUyYWI0ZThlMjhjMDBlYWU2Mjk1NzAyODgwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:27:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImtucEs2QTNEZS8xZlNVRGxwZWRJenc9PSIsInZhbHVlIjoib1dMdFpKTEZNT1BlQVR5cUF4b3RUdXlIMW9ZOE5UYnlnelB5Vk9oeVJnd0R2clZnRDNDWmdGQTI3S2xrdy9ZdmlWdlhBMG83a0JwTFBBcDNYYzN2N3lqRlVFVVRBSDBESng4K1A1QUZtMU9mTkNSZGF2elRSSzNkN3BjNzh6TFoiLCJtYWMiOiIwN2I4ZDM2NzM2ZjY1NTZlMTk0ZjViOGQ3Yzg3Yzk1YzAzNTJlZTQ3YTQ3MmVmMjE5ZmZiNzc4MDlhMmIzYWMwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6Ikh0aStJTXJsMDFSYlRRcWtJbU1rdnc9PSIsInZhbHVlIjoiZ1ZZOVVhMjRyYXNMQllscnF1eDFXd0l2eWRRMnhQdzk3MkdtTGdTaFhZWlBlR0Nxa2ZpUC9NSTN5TEZSWHhmeWZkMDdMbmppODA0Qnpzd1h5elMyQ0hlLzg4MGw2NjBid0owRzBtZkJFYThJTG1ZenEwc2lMSWVQTUpFQjNNNnAiLCJtYWMiOiIzOTQ0ZGY0NjcxNWNmNmJiZTUxY2IwYTM4ODQxNGVhM2RiYTUwYmUyYWI0ZThlMjhjMDBlYWU2Mjk1NzAyODgwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:27:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423203899\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-499987988 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://sikapal.test/admin/kantor/kapal</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499987988\", {\"maxDepth\":0})</script>\n"}}