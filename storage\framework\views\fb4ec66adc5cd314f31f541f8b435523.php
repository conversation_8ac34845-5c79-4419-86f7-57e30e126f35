<?php
use App\Helpers\HashIdHelper;
?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid p-4">
    <!-- Header Section -->
    <div class="header-section mb-5 p-4 rounded-xl position-relative overflow-hidden backdrop-blur">
        <div class="position-relative z-2">
            <div class="d-flex align-items-center mb-3">
                <div class="greeting-icon me-3 glassmorphism">
                    <i class="fas fa-ship"></i>
                </div>
                <div>
                    <h1 class="text-white mb-2 display-6 fw-bold">Manajemen Dokumen Kapal</h1>
                    <p class="text-white-50 mb-0">Kelola dokumen kapal tugboat dan tongkang</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tugboat Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-lg border-0 rounded-3">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-ship me-2"></i>
                        <PERSON><PERSON> Tugboat (<?php echo e($tugboats->count()); ?>)
                    </h4>
                </div>
                <div class="card-body p-4">
                    <?php if($tugboats->count() > 0): ?>
                        <div class="row g-4">
                            <?php $__currentLoopData = $tugboats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tugboat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="card h-100 border-0 shadow-sm hover-card">
                                        <div class="card-body p-4">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="ship-icon me-3">
                                                    <i class="fas fa-ship text-primary"></i>
                                                </div>
                                                <div>
                                                    <h5 class="card-title mb-1"><?php echo e($tugboat->nama); ?></h5>
                                                    <small class="text-muted"><?php echo e($tugboat->jenis_kapal->nama ?? 'Tugboat'); ?></small>
                                                </div>
                                            </div>
                                            
                                            <!-- Statistik Dokumen -->
                                            <div class="dokumen-stats mb-3">
                                                <div class="row text-center">
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-primary"><?php echo e($tugboat->total_dokumen); ?></div>
                                                            <div class="stat-label">Total</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-success"><?php echo e($tugboat->dokumen_berlaku); ?></div>
                                                            <div class="stat-label">Berlaku</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-danger"><?php echo e($tugboat->dokumen_tidak_berlaku); ?></div>
                                                            <div class="stat-label">Expired</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-warning"><?php echo e($tugboat->dokumen_belum_diatur); ?></div>
                                                            <div class="stat-label">Pending</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="d-grid">
                                                <a href="<?php echo e(route('dokumen.show', \App\Helpers\HashIdHelper::encode($tugboat->id))); ?>" 
                                                   class="btn btn-primary">
                                                    <i class="fas fa-folder-open me-2"></i>
                                                    Kelola Dokumen
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-ship text-muted" style="font-size: 4rem;"></i>
                            <h5 class="text-muted mt-3">Belum ada kapal tugboat</h5>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Tongkang Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0 rounded-3">
                <div class="card-header bg-success text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-ship me-2"></i>
                        Kapal Tongkang (<?php echo e($tongkangs->count()); ?>)
                    </h4>
                </div>
                <div class="card-body p-4">
                    <?php if($tongkangs->count() > 0): ?>
                        <div class="row g-4">
                            <?php $__currentLoopData = $tongkangs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tongkang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="card h-100 border-0 shadow-sm hover-card">
                                        <div class="card-body p-4">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="ship-icon me-3">
                                                    <i class="fas fa-ship text-success"></i>
                                                </div>
                                                <div>
                                                    <h5 class="card-title mb-1"><?php echo e($tongkang->nama); ?></h5>
                                                    <small class="text-muted"><?php echo e($tongkang->jenis_kapal->nama ?? 'Tongkang'); ?></small>
                                                </div>
                                            </div>
                                            
                                            <!-- Statistik Dokumen -->
                                            <div class="dokumen-stats mb-3">
                                                <div class="row text-center">
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-primary"><?php echo e($tongkang->total_dokumen); ?></div>
                                                            <div class="stat-label">Total</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-success"><?php echo e($tongkang->dokumen_berlaku); ?></div>
                                                            <div class="stat-label">Berlaku</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-danger"><?php echo e($tongkang->dokumen_tidak_berlaku); ?></div>
                                                            <div class="stat-label">Expired</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="stat-item">
                                                            <div class="stat-number text-warning"><?php echo e($tongkang->dokumen_belum_diatur); ?></div>
                                                            <div class="stat-label">Pending</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="d-grid">
                                                <a href="<?php echo e(route('dokumen.show', \App\Helpers\HashIdHelper::encode($tongkang->id))); ?>" 
                                                   class="btn btn-success">
                                                    <i class="fas fa-folder-open me-2"></i>
                                                    Kelola Dokumen
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-ship text-muted" style="font-size: 4rem;"></i>
                            <h5 class="text-muted mt-3">Belum ada kapal tongkang</h5>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px;
}

.backdrop-blur {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.8), rgba(80, 200, 120, 0.8));
}

.greeting-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.ship-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 10px;
}

.hover-card {
    transition: all 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.stat-item {
    padding: 8px 0;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dokumen-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sikapal\resources\views/admin_kantor/kapal/index.blade.php ENDPATH**/ ?>