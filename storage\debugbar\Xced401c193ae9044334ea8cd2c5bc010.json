{"__meta": {"id": "Xced401c193ae9044334ea8cd2c5bc010", "datetime": "2025-07-20 08:10:45", "utime": **********.66709, "method": "GET", "uri": "/voyage-report/accumulated-stats?", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.420449, "end": **********.667114, "duration": 0.24666500091552734, "duration_str": "247ms", "measures": [{"label": "Booting", "start": **********.420449, "relative_start": 0, "end": **********.602216, "relative_end": **********.602216, "duration": 0.18176698684692383, "duration_str": "182ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.602231, "relative_start": 0.18178200721740723, "end": **********.667116, "relative_end": 1.9073486328125e-06, "duration": 0.06488490104675293, "duration_str": "64.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24933480, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET voyage-report/accumulated-stats", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\VoyageReportController@getAccumulatedStats", "namespace": null, "prefix": "", "where": [], "as": "voyage-report.accumulated-stats", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FVoyageReportController.php&line=376\" onclick=\"\">app/Http/Controllers/VoyageReportController.php:376-413</a>"}, "queries": {"nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0038, "accumulated_duration_str": "3.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.641445, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.645565, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 83.421}, {"sql": "select\nSUM(total_waktu_berlayar_menit) as total_berlayar,\nSUM(total_waktu_berlabuh_menit) as total_berlabuh,\nSUM(total_waktu_bongkar_menit) as total_bongkar,\nSUM(konsumsi_bbm) as total_bbm,\nSUM(tonase_muatan) as total_tonase,\nCOUNT(*) as total_voyage\nfrom `voyage_reports` where `status` = 'approved' limit 1", "type": "query", "params": [], "bindings": ["approved"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/VoyageReportController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\VoyageReportController.php", "line": 400}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6549332, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "VoyageReportController.php:400", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/VoyageReportController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\VoyageReportController.php", "line": 400}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FVoyageReportController.php&line=400", "ajax": false, "filename": "VoyageReportController.php", "line": "400"}, "connection": "gema_kapal", "explain": null, "start_percent": 83.421, "width_percent": 16.579}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\VoyageReport": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FVoyageReport.php&line=1", "ajax": false, "filename": "VoyageReport.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/voyage-report/accumulated-stats\"\n]"}, "request": {"path_info": "/voyage-report/accumulated-stats", "status_code": "<pre class=sf-dump id=sf-dump-473608263 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-473608263\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1913437020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1913437020\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-278102684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-278102684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1746781761 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://sikapal.test/voyage-report</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlhseVpqNExORlNWWkhNeGFmQlNUR3c9PSIsInZhbHVlIjoiK0doNHM3NEsra21mQlNYTkJlN0cxaEI1d1ZOcTRHTXR1NkFMTE5zUWR5clZuR1F3UUVuVTNvcnJEMFIreWJzZWprWksyeG5OMUozK3lyVTN0WkFnUVlUaWpTdlB4KzBCKzZpaEFRdlAxUXgxNFZuQ3E3dFFnR3lubGlCUUFBUG4iLCJtYWMiOiJiNTE3YmU4ZDI2OWM3ZTc5NWRjYWE0NDcxZjJhZGZjZjJjOTg0NjYyYWM3MmUwOGYxMDNlNzNkNWY1MjgyYTJjIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6Ijh6d3Mya0E5NXFIdlJrY2Vzd0k4R2c9PSIsInZhbHVlIjoiN2p2cGtwTnBIV0xWVmxidDVkWVF6bTc5ZnNSRWdyR1hkY0ZaZmJzTy9mcjlDSmV3d1RTWUMvRWJMdWg5THpNbzkzL0FBbldEVlZNMnYyRnRzREhDd2F5WW0rcjgrWldxUzdUeDhORzkvZ2dtaXdKdEFCWS90YzRzQzUwRzJQU20iLCJtYWMiOiI5ZjFkMTYwNzZhN2Q0MjA0MDIxNDA2Y2RjMzdiYmM3MWEzMzU4MzdjODVjNTUzYjNiYzM0NGFhODJhMDJjYjM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746781761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:10:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJ1RklvckdjU0xzaHlFTnV3b3lkNGc9PSIsInZhbHVlIjoiTWg2L0pTWFVaRFZHWTlENUpZT20xQVRVWHlSZzlSY0pDSXBXWnhYTzc3YzdGVWN2cGJ0V1FhMUFSNituVmlGTGMzNDU5UDM5V0YrZlFXSEdCYTMyM3BLdXFJM1MwKy8zdE52OHR2T0pQRkFTWkpmcXhNcmZRVkdrRWNJLy9mdDciLCJtYWMiOiI0ZWNjNDFlYWM3M2I3ZjlmNTRlYWY4YzQxNGVkZGY2Y2EzY2JiNjE1M2VjNWU3NWYyM2FkOTVlYjY2ODI3YmFiIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:10:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6InVuTXpnb09sMXR5VnhGSXNGVHNKV3c9PSIsInZhbHVlIjoiaXZHUHIzd2Q4TFJJSzV2c2NISmpZWGNYN2g4My9rU3EzencxbXBBOXk1UzJxejc2ZGlGaUg0aE1ydkYzanpwcmZZNFh6VHlxQlpXOEF1RStTeVRFeFc0MEEvYS9MclRHR0JWOURFNVhmK0pkbmNVaE9RdVBVK3ZhblNBUWNPQ3UiLCJtYWMiOiJmOTNjMDEzYjVhOGNmOGI3MmI0ZDFjMDM1N2Q2NGYxYjY0ZTg1YTEyOWI1YTQzNzcyNTBlNDA1N2I3NDc3NjVhIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:10:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJ1RklvckdjU0xzaHlFTnV3b3lkNGc9PSIsInZhbHVlIjoiTWg2L0pTWFVaRFZHWTlENUpZT20xQVRVWHlSZzlSY0pDSXBXWnhYTzc3YzdGVWN2cGJ0V1FhMUFSNituVmlGTGMzNDU5UDM5V0YrZlFXSEdCYTMyM3BLdXFJM1MwKy8zdE52OHR2T0pQRkFTWkpmcXhNcmZRVkdrRWNJLy9mdDciLCJtYWMiOiI0ZWNjNDFlYWM3M2I3ZjlmNTRlYWY4YzQxNGVkZGY2Y2EzY2JiNjE1M2VjNWU3NWYyM2FkOTVlYjY2ODI3YmFiIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:10:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6InVuTXpnb09sMXR5VnhGSXNGVHNKV3c9PSIsInZhbHVlIjoiaXZHUHIzd2Q4TFJJSzV2c2NISmpZWGNYN2g4My9rU3EzencxbXBBOXk1UzJxejc2ZGlGaUg0aE1ydkYzanpwcmZZNFh6VHlxQlpXOEF1RStTeVRFeFc0MEEvYS9MclRHR0JWOURFNVhmK0pkbmNVaE9RdVBVK3ZhblNBUWNPQ3UiLCJtYWMiOiJmOTNjMDEzYjVhOGNmOGI3MmI0ZDFjMDM1N2Q2NGYxYjY0ZTg1YTEyOWI1YTQzNzcyNTBlNDA1N2I3NDc3NjVhIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:10:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-907534122 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">http://sikapal.test/voyage-report/accumulated-stats</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907534122\", {\"maxDepth\":0})</script>\n"}}