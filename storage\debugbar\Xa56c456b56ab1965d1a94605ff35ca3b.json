{"__meta": {"id": "Xa56c456b56ab1965d1a94605ff35ca3b", "datetime": "2025-07-20 08:09:18", "utime": **********.516871, "method": "GET", "uri": "/checklist-engine/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[08:09:18] LOG.info: Kapal: {\n    \"id\": 4,\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.239495, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752973757.991611, "end": **********.516897, "duration": 0.5252859592437744, "duration_str": "525ms", "measures": [{"label": "Booting", "start": 1752973757.991611, "relative_start": 0, "end": **********.175219, "relative_end": **********.175219, "duration": 0.1836080551147461, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.175233, "relative_start": 0.18362188339233398, "end": **********.516901, "relative_end": 4.0531158447265625e-06, "duration": 0.34166812896728516, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33330752, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "checklist-engine.index", "param_count": null, "params": [], "start": **********.466448, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/checklist-engine/index.blade.phpchecklist-engine.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fchecklist-engine%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.474205, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "vendor.pagination.custom", "param_count": null, "params": [], "start": **********.492491, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/vendor/pagination/custom.blade.phpvendor.pagination.custom", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fcustom.blade.php&line=1", "ajax": false, "filename": "custom.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.494393, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.495534, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.50892, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET checklist-engine/{id}", "middleware": "web", "controller": "App\\Http\\Controllers\\ChecklistEngineController@index", "namespace": null, "prefix": "", "where": [], "as": "checklist-engine.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=19\" onclick=\"\">app/Http/Controllers/ChecklistEngineController.php:19-133</a>"}, "queries": {"nb_statements": 344, "nb_visible_statements": 345, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08555000000000004, "accumulated_duration_str": "85.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 244 queries only show the query. Limit can be raised in the config. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.22257, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2275531, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:23", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=23", "ajax": false, "filename": "ChecklistEngineController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 4.711}, {"sql": "select * from `jenis_kapal` where `jenis_kapal`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.242362, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:34", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=34", "ajax": false, "filename": "ChecklistEngineController.php", "line": "34"}, "connection": "gema_kapal", "explain": null, "start_percent": 4.711, "width_percent": 1.274}, {"sql": "select * from `histori_per<PERSON>lanan_kapal` where `kapal_id` = 4 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.244673, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:47", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=47", "ajax": false, "filename": "ChecklistEngineController.php", "line": "47"}, "connection": "gema_kapal", "explain": null, "start_percent": 5.985, "width_percent": 0.584}, {"sql": "select `hpe`.*, `cde`.`item_pemeriksaan` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`history_id` = 24", "type": "query", "params": [], "bindings": [4, 24], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2465432, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:55", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=55", "ajax": false, "filename": "ChecklistEngineController.php", "line": "55"}, "connection": "gema_kapal", "explain": null, "start_percent": 6.569, "width_percent": 0.748}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.248369, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 7.317, "width_percent": 0.923}, {"sql": "select `hpe`.*, `cde`.`item_pemeri<PERSON><PERSON>`, `hpk`.`tujuan`, `hpk`.`rh_me_ps`, `hpk`.`rh_me_sb` from `hasil_pemeriksaan_engine` as `hpe` left join `checklist_data_engine` as `cde` on `hpe`.`item_id` = `cde`.`id` left join `histori_per<PERSON><PERSON>n_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 order by `hpe`.`tanggal_pemeriksaan` desc, `hpe`.`created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2502751, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:93", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=93", "ajax": false, "filename": "ChecklistEngineController.php", "line": "93"}, "connection": "gema_kapal", "explain": null, "start_percent": 8.241, "width_percent": 0.561}, {"sql": "select count(*) as aggregate from `checklist_engines` where `kapal_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.252693, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ChecklistEngineController.php:99", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FChecklistEngineController.php&line=99", "ajax": false, "filename": "ChecklistEngineController.php", "line": "99"}, "connection": "gema_kapal", "explain": null, "start_percent": 8.802, "width_percent": 0.853}, {"sql": "select * from `checklist_data_engine` order by `waktu` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.2549112, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:24", "source": {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=24", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 9.655, "width_percent": 0.748}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.260025, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 10.403, "width_percent": 0.561}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 42 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 42, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2615418, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 10.964, "width_percent": 0.491}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.263126, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 11.455, "width_percent": 0.421}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.264794, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 11.876, "width_percent": 0.386}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.26697, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 12.262, "width_percent": 0.479}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 44 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 44, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.268366, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 12.741, "width_percent": 0.538}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.269969, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 13.279, "width_percent": 0.491}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.27159, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 13.77, "width_percent": 0.561}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.273166, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 14.331, "width_percent": 0.362}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 54 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 54, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.274445, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 14.693, "width_percent": 0.538}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.275955, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 15.231, "width_percent": 0.351}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.277549, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 15.582, "width_percent": 0.573}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 58 limit 1", "type": "query", "params": [], "bindings": [58], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2794142, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 16.154, "width_percent": 0.316}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 58 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 58, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2807322, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 16.47, "width_percent": 0.76}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.282472, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.23, "width_percent": 0.304}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.283822, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 17.534, "width_percent": 0.631}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.285494, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.165, "width_percent": 0.187}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 65 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 65, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2866209, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.352, "width_percent": 0.503}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.288078, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 18.854, "width_percent": 0.199}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.289268, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.053, "width_percent": 0.339}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.29071, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.392, "width_percent": 0.269}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 72 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 72, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2919068, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 19.661, "width_percent": 0.479}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.293319, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.14, "width_percent": 0.199}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.294508, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.339, "width_percent": 0.386}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 87 limit 1", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2958841, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.725, "width_percent": 0.164}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 87 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 87, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.296961, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 20.888, "width_percent": 0.526}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.298468, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.414, "width_percent": 0.175}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.299625, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.59, "width_percent": 0.397}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 88 limit 1", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3010292, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 21.987, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 88 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 88, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3020859, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.116, "width_percent": 0.491}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.303524, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.607, "width_percent": 0.187}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3048, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 22.794, "width_percent": 0.62}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 94 limit 1", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.306544, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.413, "width_percent": 0.234}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 94 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 94, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.307759, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 23.647, "width_percent": 0.573}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.309306, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.22, "width_percent": 0.21}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.31052, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.43, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 96 limit 1", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.312071, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 24.863, "width_percent": 0.409}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 96 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 96, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3134558, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.272, "width_percent": 0.503}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.314922, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.774, "width_percent": 0.21}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3161302, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 25.985, "width_percent": 0.327}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 99 limit 1", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.317464, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.312, "width_percent": 0.175}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 99 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 99, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.318615, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.487, "width_percent": 0.397}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.319973, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 26.885, "width_percent": 0.152}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3211029, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.037, "width_percent": 0.304}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 106 limit 1", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3223991, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.341, "width_percent": 0.164}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 106 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 106, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.323507, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.504, "width_percent": 0.409}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.324867, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 27.914, "width_percent": 0.234}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.326144, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.147, "width_percent": 0.807}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 109 limit 1", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.327941, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 28.954, "width_percent": 0.222}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 109 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 109, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.329079, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.176, "width_percent": 0.503}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.330578, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.679, "width_percent": 0.21}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.331778, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 29.889, "width_percent": 0.596}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 112 limit 1", "type": "query", "params": [], "bindings": [112], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.333414, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.485, "width_percent": 0.175}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 112 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 112, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.334499, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 30.66, "width_percent": 0.503}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 118 limit 1", "type": "query", "params": [], "bindings": [118], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3360322, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.163, "width_percent": 0.175}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 118 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 118, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3371449, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.338, "width_percent": 0.444}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 120 limit 1", "type": "query", "params": [], "bindings": [120], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.338571, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.783, "width_percent": 0.164}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 120 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 120, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.339773, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 31.946, "width_percent": 0.666}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 123 limit 1", "type": "query", "params": [], "bindings": [123], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.341467, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.613, "width_percent": 0.187}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 123 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 123, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.342563, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 32.8, "width_percent": 0.444}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 131 limit 1", "type": "query", "params": [], "bindings": [131], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.343975, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.244, "width_percent": 0.164}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 131 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 131, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.345062, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.407, "width_percent": 0.456}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 134 limit 1", "type": "query", "params": [], "bindings": [134], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3466341, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 33.863, "width_percent": 0.175}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 134 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 134, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3477259, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.039, "width_percent": 0.444}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 138 limit 1", "type": "query", "params": [], "bindings": [138], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3491561, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.483, "width_percent": 0.175}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 138 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 138, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.350281, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 34.658, "width_percent": 0.362}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 139 limit 1", "type": "query", "params": [], "bindings": [139], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.351638, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.02, "width_percent": 0.187}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 139 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 139, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.352792, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.207, "width_percent": 0.456}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 140 limit 1", "type": "query", "params": [], "bindings": [140], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3543742, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.663, "width_percent": 0.152}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 140 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 140, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.355469, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 35.815, "width_percent": 0.386}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 141 limit 1", "type": "query", "params": [], "bindings": [141], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356847, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.201, "width_percent": 0.164}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 141 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 141, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.357935, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.365, "width_percent": 0.362}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 143 limit 1", "type": "query", "params": [], "bindings": [143], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.359287, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.727, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 143 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 143, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.360512, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 36.856, "width_percent": 0.631}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3621778, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.487, "width_percent": 0.281}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.363492, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 37.767, "width_percent": 0.421}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.364904, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.188, "width_percent": 0.222}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 18 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 18, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.366035, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 38.41, "width_percent": 0.877}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.367966, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.287, "width_percent": 0.245}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 22 limit 1", "type": "query", "params": [], "bindings": [4, 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.369215, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 39.532, "width_percent": 0.479}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 50 limit 1", "type": "query", "params": [], "bindings": [50], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.370754, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.012, "width_percent": 0.257}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 50 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 50, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3719418, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.269, "width_percent": 0.479}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.373362, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.748, "width_percent": 0.199}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.374715, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 40.947, "width_percent": 0.608}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 52 limit 1", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.376386, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.555, "width_percent": 0.187}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 52 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 52, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.377514, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 41.742, "width_percent": 0.468}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.378929, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.209, "width_percent": 0.199}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.380157, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.408, "width_percent": 0.421}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, {"index": 21, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.381623, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:167", "source": {"index": 20, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 167}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=167", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "167"}, "connection": "gema_kapal", "explain": null, "start_percent": 42.829, "width_percent": 0.199}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = 4 and `hpe`.`item_id` = 93 and `hpe`.`kondisi` = 'bagus' order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [4, 93, "bagus"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.382726, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:180", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=180", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "180"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.027, "width_percent": 0.491}, {"sql": "select * from `kapal` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.384167, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:190", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=190", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "190"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.518, "width_percent": 0.257}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = 4 and `id` > 12 limit 1", "type": "query", "params": [], "bindings": [4, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, {"index": 15, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 57}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ChecklistEngineController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\ChecklistEngineController.php", "line": 107}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.385416, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ChecklistDataEngine.php:206", "source": {"index": 14, "namespace": null, "name": "app/Models/ChecklistDataEngine.php", "file": "C:\\laragon\\www\\sikapal\\app\\Models\\ChecklistDataEngine.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=206", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "206"}, "connection": "gema_kapal", "explain": null, "start_percent": 43.776, "width_percent": 0.327}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.38673, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 44.103, "width_percent": 0.152}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.386971, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 44.255, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3872151, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 44.454, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3874319, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 44.559, "width_percent": 0.701}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3881378, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 45.26, "width_percent": 0.292}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3885088, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 45.552, "width_percent": 0.292}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388807, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 45.845, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.388974, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 45.973, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3892062, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.148, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389389, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.254, "width_percent": 0.21}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389611, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.464, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.389764, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.581, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3899572, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.721, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390162, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 46.826, "width_percent": 0.316}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390494, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.142, "width_percent": 0.14}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390673, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.282, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391129, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.715, "width_percent": 0.14}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391351, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 47.855, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391592, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.077, "width_percent": 0.316}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391936, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.393, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392183, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.568, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39238, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.673, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392611, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 48.895, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39277, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.024, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392983, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.176, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39316, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.269, "width_percent": 0.187}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3933918, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.456, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393574, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.55, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.393805, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 49.725, "width_percent": 0.281}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3941822, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.006, "width_percent": 0.316}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394573, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.321, "width_percent": 0.292}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.394974, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 50.614, "width_percent": 0.409}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.395391, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.023, "width_percent": 0.175}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3956041, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.198, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3958292, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.362, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39603, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.467, "width_percent": 0.234}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3962982, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.701, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39649, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.806, "width_percent": 0.187}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3967159, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 51.993, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.396894, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.086, "width_percent": 0.234}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3971422, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.32, "width_percent": 0.351}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397512, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.671, "width_percent": 0.187}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.397756, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.858, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3979719, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 52.975, "width_percent": 0.234}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.398226, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.209, "width_percent": 0.316}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39857, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.524, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3988042, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.7, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399011, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 53.816, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3992472, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.039, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39941, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.167, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3996181, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.319, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399806, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.424, "width_percent": 0.21}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400028, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.635, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40018, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.752, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400384, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 54.892, "width_percent": 0.281}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40075, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.172, "width_percent": 0.292}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4010851, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.465, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401326, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 55.593, "width_percent": 0.62}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4019482, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.213, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402157, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.33, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4023662, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.528, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.402524, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.657, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40274, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.821, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40293, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 56.926, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40314, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.124, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403285, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.241, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4034948, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.393, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403667, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.487, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.403879, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.686, "width_percent": 0.281}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40417, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 57.966, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4043708, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.106, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.404558, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.2, "width_percent": 0.432}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40499, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.632, "width_percent": 0.152}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405177, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.784, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4054122, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 58.96, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405614, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.065, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.405854, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.287, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406021, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.416, "width_percent": 0.339}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4064012, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 59.755, "width_percent": 0.316}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406801, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.07, "width_percent": 0.584}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4073489, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 60.655, "width_percent": 0.421}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.407773, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.075, "width_percent": 0.374}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4082088, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.449, "width_percent": 0.304}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.408615, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 61.753, "width_percent": 0.62}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409234, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.373, "width_percent": 0.187}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.40947, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.56, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4097102, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.735, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.409939, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 62.852, "width_percent": 0.421}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410391, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.273, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.410592, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.39, "width_percent": 0.21}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4108589, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.6, "width_percent": 0.304}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411248, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 63.904, "width_percent": 0.257}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411556, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.161, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.411794, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.29, "width_percent": 0.292}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412124, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.582, "width_percent": 0.397}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412549, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 64.98, "width_percent": 0.409}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413012, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.389, "width_percent": 0.316}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413421, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 65.704, "width_percent": 0.409}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413844, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.113, "width_percent": 0.409}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414268, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.523, "width_percent": 0.21}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414547, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 66.733, "width_percent": 0.281}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.414928, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.013, "width_percent": 0.362}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.415308, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.376, "width_percent": 0.386}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.41572, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 67.762, "width_percent": 0.491}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416272, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.252, "width_percent": 0.304}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4166992, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.556, "width_percent": 0.316}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417024, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 68.872, "width_percent": 0.269}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417319, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.141, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417582, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.34, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417804, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.456, "width_percent": 0.222}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4180732, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.679, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418282, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.795, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418523, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 69.994, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418716, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.099, "width_percent": 0.281}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419023, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.38, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419208, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.485, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419644, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 70.918, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.419838, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.034, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420076, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.233, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420252, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.327, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420465, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.525, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.420631, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.654, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4208481, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.818, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421056, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 71.923, "width_percent": 0.245}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4213312, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.168, "width_percent": 0.14}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421514, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.309, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4217322, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.461, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.421935, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.566, "width_percent": 0.339}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4222949, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 72.905, "width_percent": 0.456}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.422758, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.361, "width_percent": 0.421}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423211, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.781, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423424, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 73.898, "width_percent": 0.234}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423671, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.132, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4238331, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.261, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4240391, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.413, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424234, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.518, "width_percent": 0.21}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4244592, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.728, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424612, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.845, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4248068, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 74.985, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424993, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.079, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.425204, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.278, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42537, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.395, "width_percent": 0.386}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4257991, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.78, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426034, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 75.909, "width_percent": 0.713}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426696, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.622, "width_percent": 0.152}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426879, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.774, "width_percent": 0.222}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427153, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 76.996, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427365, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.113, "width_percent": 0.234}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.427628, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.347, "width_percent": 0.257}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4279249, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.604, "width_percent": 0.222}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428224, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.826, "width_percent": 0.129}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428449, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 77.954, "width_percent": 0.245}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4287071, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.2, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.428882, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.328, "width_percent": 0.269}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4292371, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 78.597, "width_percent": 0.584}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.429878, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.182, "width_percent": 0.316}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430197, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.497, "width_percent": 0.14}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430367, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.638, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43058, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.79, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.430769, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 79.883, "width_percent": 0.21}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4309971, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.094, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431152, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.21, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431345, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.351, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431524, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.444, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43173, "duration": 0.00011, "duration_str": "110μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.643, "width_percent": 0.129}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43189, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.771, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432086, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 80.912, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4322672, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.005, "width_percent": 0.327}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432593, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.333, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43276, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.449, "width_percent": 0.514}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433295, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 81.964, "width_percent": 0.152}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433536, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.116, "width_percent": 0.245}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43379, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.361, "width_percent": 0.14}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433959, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.501, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4341722, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.665, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434359, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.77, "width_percent": 0.21}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4345832, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 82.981, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4347281, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.098, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434933, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.25, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.435108, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.343, "width_percent": 0.199}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.435318, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.542, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4354692, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.659, "width_percent": 0.14}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4356709, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 83.799, "width_percent": 0.292}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4360552, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.091, "width_percent": 0.736}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.43675, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 84.828, "width_percent": 0.245}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437022, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.073, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437266, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.248, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437465, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.354, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4377139, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.552, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.437894, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.646, "width_percent": 0.187}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438125, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.833, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438298, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 85.926, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438521, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.102, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.438702, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.195, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4389348, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.371, "width_percent": 0.281}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439302, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.651, "width_percent": 0.292}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4396071, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 86.943, "width_percent": 0.339}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.439962, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.282, "width_percent": 0.245}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4402542, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.528, "width_percent": 0.117}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44046, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.645, "width_percent": 0.199}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.440713, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.843, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.440894, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 87.937, "width_percent": 0.21}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4411478, "duration": 8e-05, "duration_str": "80μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.147, "width_percent": 0.094}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44132, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.241, "width_percent": 0.175}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.441549, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.416, "width_percent": 0.327}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442009, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 88.743, "width_percent": 0.456}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442623, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.199, "width_percent": 0.269}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.442991, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 89.468, "width_percent": 0.631}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.443616, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.099, "width_percent": 0.468}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444092, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.567, "width_percent": 0.351}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4444869, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 90.918, "width_percent": 0.292}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.444855, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.21, "width_percent": 0.234}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445097, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.444, "width_percent": 0.14}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445274, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.584, "width_percent": 0.152}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4454832, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.736, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.445693, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 91.841, "width_percent": 0.432}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44612, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.274, "width_percent": 0.152}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44631, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.425, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4465342, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.589, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.446783, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 92.694, "width_percent": 0.748}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447489, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.442, "width_percent": 0.152}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447679, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.594, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.447901, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.758, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448093, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 93.863, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448331, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.085, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4484892, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.202, "width_percent": 0.14}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4486861, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.342, "width_percent": 0.164}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4488811, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.506, "width_percent": 0.14}, {"sql": "select count(*) as aggregate from `hasil_pemeriksaan_engine` where `kapal_id` = ? and `item_id` = ? and `kondisi` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.449069, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 94.646, "width_percent": 0.432}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.449538, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.079, "width_percent": 0.199}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.449831, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 95.278, "width_percent": 0.888}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.450645, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.166, "width_percent": 0.175}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.450849, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.341, "width_percent": 0.164}, {"sql": "select * from `checklist_data_engine` where `checklist_data_engine`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.45106, "duration": 8.999999999999999e-05, "duration_str": "90μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.505, "width_percent": 0.105}, {"sql": "select `hpk`.`id` as `history_id`, `hpe`.`created_at` from `hasil_pemeriksaan_engine` as `hpe` inner join `histori_per<PERSON><PERSON><PERSON>_kapal` as `hpk` on `hpe`.`history_id` = `hpk`.`id` where `hpe`.`kapal_id` = ? and `hpe`.`item_id` = ? and `hpe`.`kondisi` = ? order by `hpe`.`created_at` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451261, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.61, "width_percent": 0.222}, {"sql": "select * from `kapal` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451498, "duration": 0.0001, "duration_str": "100μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.832, "width_percent": 0.117}, {"sql": "select COALESCE(SUM(rh_me_ps), 0) as total_ps, COALESCE(SUM(rh_me_sb), 0) as total_sb from `histori_perjalanan_kapal` where `kapal_id` = ? and `id` > ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451652, "duration": 0.00011999999999999999, "duration_str": "120μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 96.949, "width_percent": 0.14}, {"sql": "select distinct `tujuan` from `histori_perjalanan_kapal` where `kapal_id` = ? and `tujuan` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4519331, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.089, "width_percent": 0.175}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.47134, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.265, "width_percent": 0.386}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.471757, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.65, "width_percent": 0.257}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.472427, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 97.908, "width_percent": 0.643}, {"sql": "select count(*) as aggregate from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473097, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.551, "width_percent": 0.199}, {"sql": "select * from `histori_perjalanan_kapal` where `histori_perjalanan_kapal`.`kapal_id` = ? and `histori_perjalanan_kapal`.`kapal_id` is not null order by `created_at` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.473341, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 98.749, "width_percent": 0.386}, {"sql": "select * from `users` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.504945, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "gema_kapal", "explain": null, "start_percent": 99.135, "width_percent": 0.865}]}, "models": {"data": {"App\\Models\\ChecklistDataEngine": {"value": 273, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FChecklistDataEngine.php&line=1", "ajax": false, "filename": "ChecklistDataEngine.php", "line": "?"}}, "App\\Models\\HistoriPerjalananKapal": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FHistoriPerjalananKapal.php&line=1", "ajax": false, "filename": "HistoriPerjalananKapal.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}, "App\\Models\\JenisKapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FJenisKapal.php&line=1", "ajax": false, "filename": "JenisKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 294, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/checklist-engine/2NaDK50q4p\"\n]"}, "request": {"path_info": "/checklist-engine/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-1609978978 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1609978978\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-883667039 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-883667039\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1331145217 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1331145217\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1240465007 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlM1WGkzajgyYWNjS0djSTB0LzNkdmc9PSIsInZhbHVlIjoiS2MvdEFVVWd4U24yK1pDZkhYd1ZIMWd6R2w5T21sOGwrMjB4Mm1pQ2k5aTBuZGR5VVU3VktubWVONmpBelMwTnBmMWRFRmNKSjhuK0c4WjRmMUExUHBLT0I5SmE4MGwwRHN1Ujc3dFJ6bGZoL0NEQXRJbDczaVZrNm9PN0VOWlgiLCJtYWMiOiI0ZGI4ZDM0ZGQ1NmFhNzM2Y2QxYjgwMTVjNzVhYTQ0YjFjZmYzODY4ZDc0NjQyNDEyMDlhY2ZiZWMwM2EzNGVmIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6ImsxbTQ5cVFqZ3UyWnlGTyt5NEQ3SFE9PSIsInZhbHVlIjoiRmcraTVTSVJVQ3NQWkVCbnZSaGZnOTY2dFYxaXVnRmprZENMR0dlYytGZlRRYTQ4ODlqSEtHSVprMzZBcExsTEpWRnVHbnlwZFVrMktaR0pjRWRSVFlIRm1LZDljQmViQ0NRN2RVQTRvbFdCeDFtOVUwVUhMV3BHOWlNQzczMnkiLCJtYWMiOiI0Yzg3YTQyMWUyZjQ2M2NhOWU5NGU4M2FhMTFlMDBlNzVlZWRiNmFiYWZmNzY4MTE1MmVhNTgzNTlmMmRjNjg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240465007\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-547372553 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547372553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1213877459 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:09:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxYREp1SFAwL1Bjd2NHbFlVUG5lZHc9PSIsInZhbHVlIjoiSjRIdWFrRGlKelRIc0pocTRBRXliSjY2UzhiZ2FaUTZYWjZzUUNxY2JtU2ovUzlBZUFaQjgzNENLRUdiTys2aXd1MXdjc0tnY2FEbFhZZHhSK1hzV0o4VjJKN090a05qZ0VtdHQxdHNZVjEweHRiRC9uMTFWVTdmR0pBaXA1OEkiLCJtYWMiOiJkMzA2MGQ3MjdiZTQ2ZGU1MzkwNTA5NmQzOGZiNDRjMzY3MjE4NjdmODQ0YTA5MTRmOTllM2QyODliNmRhYWJmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImJ6bExjWStzaUZNTTFFZ1pRR08yZnc9PSIsInZhbHVlIjoiY29GRllta1ByZjZDY2hldmNUU3J3UUpOdjVxN0dJbEd1NXpEdlhlUUxLVHh3c0d6M2lndFdodituTmgwN3luYlY1NHFIemhFQmNKQllRa0wyb3U0R3pNUkN0OXJKeS9XbG5heEUvajBpSTBocGNwL1Z1T2pvNW0vS3I3OGgzNXYiLCJtYWMiOiIzMDhhNTJmMzllYzUwYjE0NzJkNWY0ZDc5NmI3YmE1MjZkYTY1ZDE3ZTJiNzRjMDM1YmFjZjUxYTdjZjkwNzcwIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:09:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxYREp1SFAwL1Bjd2NHbFlVUG5lZHc9PSIsInZhbHVlIjoiSjRIdWFrRGlKelRIc0pocTRBRXliSjY2UzhiZ2FaUTZYWjZzUUNxY2JtU2ovUzlBZUFaQjgzNENLRUdiTys2aXd1MXdjc0tnY2FEbFhZZHhSK1hzV0o4VjJKN090a05qZ0VtdHQxdHNZVjEweHRiRC9uMTFWVTdmR0pBaXA1OEkiLCJtYWMiOiJkMzA2MGQ3MjdiZTQ2ZGU1MzkwNTA5NmQzOGZiNDRjMzY3MjE4NjdmODQ0YTA5MTRmOTllM2QyODliNmRhYWJmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImJ6bExjWStzaUZNTTFFZ1pRR08yZnc9PSIsInZhbHVlIjoiY29GRllta1ByZjZDY2hldmNUU3J3UUpOdjVxN0dJbEd1NXpEdlhlUUxLVHh3c0d6M2lndFdodituTmgwN3luYlY1NHFIemhFQmNKQllRa0wyb3U0R3pNUkN0OXJKeS9XbG5heEUvajBpSTBocGNwL1Z1T2pvNW0vS3I3OGgzNXYiLCJtYWMiOiIzMDhhNTJmMzllYzUwYjE0NzJkNWY0ZDc5NmI3YmE1MjZkYTY1ZDE3ZTJiNzRjMDM1YmFjZjUxYTdjZjkwNzcwIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:09:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213877459\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1933569156 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://sikapal.test/checklist-engine/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933569156\", {\"maxDepth\":0})</script>\n"}}