@extends('layouts.app')

@section('content')
<style>
    .cursor-pointer {
        cursor: pointer;
    }
    .cursor-pointer:hover {
        background-color: rgba(0,0,0,0.05);
    }

    /* Responsive Styles */
    .dashboard-wrapper {
        padding: 1rem;
    }

    .modern-card {
        margin-bottom: 1rem;
    }

    .notification-card {
        height: 100%;
        background: #fff;
        border-radius: 10px;
        padding: 1.25rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border: 1px solid rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .vessel-card {
        height: 100%;
        padding: 1.5rem;
    }

    /* Mobile Styles */
    @media (max-width: 576px) {
        .dashboard-wrapper {
            padding: 0.5rem;
        }

        .card-title {
            font-size: 1.1rem;
        }

        .notification-card {
            padding: 1rem;
        }

        .vessel-stats {
            flex-direction: column;
        }

        .stat-item {
            width: 100%;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 1.2rem;
        }
    }

    /* Tablet Styles */
    @media (min-width: 577px) and (max-width: 991px) {
        .stat-item {
            padding: 0.75rem;
        }
    }

    /* Ensure proper spacing and alignment */
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .col-md-6, .col-lg-4 {
        padding: 0.5rem;
    }

    /* Responsive Navigation */
    .modern-pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .page-item {
        margin: 0.25rem;
    }

    /* Responsive Icons */
    .notification-icon {
        min-width: 45px;
        height: 45px;
    }

    /* Improve text readability on small screens */
    .stat-label {
        font-size: 0.9rem;
        line-height: 1.2;
    }

    /* Ensure buttons are touch-friendly */
    .btn {
        min-height: 44px;
        padding: 0.5rem 1rem;
    }
</style>

<div class="dashboard-wrapper">
    <!-- Notifikasi Section -->
    <div class="card modern-card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title">
                    <i class="fas fa-bell me-2"></i>
                    Notifikasi
                    @if($notifications->count() > 0)
                        <span class="notification-badge">{{ $notifications->total() }}</span>
                    @endif
                </h5>
                <button id="toggleNotifications" class="btn-modern-outline">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
        </div>
        <div class="card-body" id="notificationBody">
            @if($notifications->count() > 0)
                <div class="notification-list" id="notificationList">
                    @include('dashboard.notification-items')
                </div>
                
                <div class="d-flex justify-content-center mt-4">
                    <nav aria-label="Page navigation">
                        <ul class="pagination modern-pagination" id="notificationPagination">
                            {{-- Previous Page Link --}}
                            <li class="page-item {{ $notifications->onFirstPage() ? 'disabled' : '' }}" 
                                data-page="{{ $notifications->currentPage() - 1 }}">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            </li>

                            {{-- Page Numbers --}}
                            @for ($i = 1; $i <= $notifications->lastPage(); $i++)
                                <li class="page-item {{ $i == $notifications->currentPage() ? 'active' : '' }}" 
                                    data-page="{{ $i }}">
                                    <span class="page-link">{{ $i }}</span>
                                </li>
                            @endfor

                            {{-- Next Page Link --}}
                            <li class="page-item {{ !$notifications->hasMorePages() ? 'disabled' : '' }}" 
                                data-page="{{ $notifications->currentPage() + 1 }}">
                                <span class="page-link">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </li>
                        </ul>
                    </nav>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-check-circle text-success fa-2x mb-3"></i>
                    <p class="text-muted">Tidak ada notifikasi</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Notifikasi Checklist Engine -->
    @if(!empty($tugboatStats['engine_notifications']))
    <div class="card modern-card mb-4">
        <div class="card-header bg-warning text-dark">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Notifikasi Checklist Engine
                    <span class="badge bg-danger ms-2">
                        {{ collect($tugboatStats['engine_notifications'])->map(function($notifications) {
                            return collect($notifications)->filter(function($notif) {
                                return isset($notif['interval']) && $notif['interval'] !== null;
                            })->count();
                        })->sum() }}
                    </span>
                </h5>
                <button id="toggleEngineNotifications" class="btn-modern-outline">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
        </div>
        <div class="card-body" id="engineNotificationBody">
            <div class="row g-3">
                @foreach($tugboatStats['engine_notifications'] as $kapalNama => $notifications)
                    @php
                        $notifRutin = collect($notifications)->filter(function($notif) {
                            return isset($notif['interval']);
                        });
                        
                        // Skip jika tidak ada notifikasi rutin
                        if ($notifRutin->isEmpty()) continue;
                        
                        // Cari kapal untuk mendapatkan hash_id
                        $kapal = $tugboats->where('nama', $kapalNama)->first();
                    @endphp

                    @if($kapal)
                    <div class="col-md-6 col-lg-4">
                        <div class="notification-card">
                            <div class="d-flex align-items-center mb-3">
                                <div class="notification-icon">
                                    <i class="fas fa-ship"></i>
                                </div>
                                <div class="ms-3">
                                    <h6 class="mb-1">{{ $kapalNama }}</h6>
                                    <span class="badge bg-danger">
                                        {{ $notifRutin->count() }} item perlu diperiksa
                                    </span>
                                </div>
                            </div>
                            <a href="{{ route('checklist-engine.index', $kapal->hash_id) }}" 
                               class="btn btn-primary w-100 d-flex align-items-center justify-content-center">
                                <i class="fas fa-external-link-alt me-2"></i>
                                Lihat Detail Checklist
                            </a>
                        </div>
                    </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <style>
        .notification-card {
            background: #fff;
            border-radius: 10px;
            padding: 1.25rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .notification-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .notification-icon i {
            color: white;
            font-size: 1.25rem;
        }
        
        .notification-card .btn-primary {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            border: none;
            padding: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .notification-card .btn-primary:hover {
            background: linear-gradient(135deg, #182848 0%, #4b6cb7 100%);
            transform: translateY(-1px);
        }
        
        .notification-card h6 {
            color: #2d3748;
            font-weight: 600;
        }
    </style>

    <!-- Main Cards Section -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="vessel-card tugboat-card">
                <div class="vessel-icon">
                    <i class="fas fa-ship"></i>
                </div>
                <div class="vessel-info">
                    <h2>Tugboat</h2>
                    <div class="vessel-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $tugboatStats['total'] }}</span>
                            <span class="stat-label">Total Kapal</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $tugboatStats['dokumen'] }}</span>
                            <span class="stat-label">Total Dokumen</span>
                        </div>
                        <div class="stat-item stat-item-wide">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <span class="stat-label">Checklist Perawatan Mesin Jangkar</span>
                                    @if($tugboatStats['last_check'])
                                        <div class="checklist-stats">
                                            <span class="stat-number">
                                                <span class="text-success">{{ $tugboatStats['checklist_baik'] }}</span>
                                                <span class="separator">/</span>
                                                <span class="text-danger">{{ $tugboatStats['checklist_buruk'] }}</span>
                                            </span>
                                            <span class="stat-sublabel">{{ $tugboatStats['checklist_baik'] }} Baik / {{ $tugboatStats['checklist_buruk'] }} Tidak</span>
                                            <span class="last-check">
                                                <i class="far fa-clock me-1"></i>
                                                Terakhir: {{ $tugboatStats['last_check'] }}
                                            </span>
                                        </div>
                                    @else
                                        <div class="checklist-stats empty-checklist">
                                            <span class="no-data">Belum ada checklist</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <span class="stat-label">Checklist Perawatan Engine</span>
                                    @if(isset($tugboatStats['engine_last_check']))
                                        <div class="checklist-stats">
                                            <span class="stat-number">
                                                <span class="text-success">{{ $tugboatStats['engine_checklist_baik'] }}</span>
                                                <span class="separator">/</span>
                                                <span class="text-danger">{{ $tugboatStats['engine_checklist_buruk'] }}</span>
                                            </span>
                                            <span class="stat-sublabel">{{ $tugboatStats['engine_checklist_baik'] }} Baik / {{ $tugboatStats['engine_checklist_buruk'] }} Tidak</span>
                                            <span class="last-check">
                                                <i class="far fa-clock me-1"></i>
                                                Terakhir: {{ $tugboatStats['engine_last_check'] }}
                                            </span>
                                        </div>
                                    @else
                                        <div class="checklist-stats empty-checklist">
                                            <span class="no-data">Belum ada checklist</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <button onclick="showTugboatModal()" class="btn-detail">
                        <span>Lihat Detail</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
                <div class="vessel-backdrop tugboat-backdrop"></div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="vessel-card tongkang-card">
                <div class="vessel-icon">
                    <i class="fas fa-anchor"></i>
                </div>
                <div class="vessel-info">
                    <h2>Tongkang</h2>
                    <div class="vessel-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ $tongkangStats['total'] }}</span>
                            <span class="stat-label">Total Kapal</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ $tongkangStats['dokumen'] }}</span>
                            <span class="stat-label">Total Dokumen</span>
                        </div>
                        <div class="stat-item stat-item-wide">
                            <span class="stat-label">Checklist Perawatan Mesin Jangkar</span>
                            @if($tongkangStats['last_check'])
                                <div class="checklist-stats">
                                    <span class="stat-number">
                                        <span class="text-success">{{ $tongkangStats['checklist_baik'] }}</span>
                                        <span class="separator">/</span>
                                        <span class="text-danger">{{ $tongkangStats['checklist_buruk'] }}</span>
                                    </span>
                                    <span class="stat-sublabel">{{ $tongkangStats['checklist_baik'] }} Baik / {{ $tongkangStats['checklist_buruk'] }} Tidak</span>
                                    <span class="last-check">
                                        <i class="far fa-clock me-1"></i>
                                        Terakhir: {{ $tongkangStats['last_check'] }}
                                    </span>
                                </div>
                            @else
                                <div class="checklist-stats empty-checklist">
                                    <span class="no-data">Belum ada checklist</span>
                                </div>
                            @endif
                        </div>
                    </div>
                    <button onclick="showTongkangModal()" class="btn-detail">
                        <span>Lihat Detail</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
                <div class="vessel-backdrop tongkang-backdrop"></div>
            </div>
        </div>
    </div>

    <!-- Kapal Management Section -->
    <div class="card modern-card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title">Manajemen Kapal</h5>
                <button id="toggleKapal" class="add-ship-btn">
                    <div class="btn-content">
                        <i class="fas fa-plus"></i>
                        <span class="btn-text">Tambahkan Kapal Baru</span>
                    </div>
                </button>
            </div>
        </div>
        <div id="formKapal" class="card-body" style="display: none;">
            <div class="row g-4 justify-content-center">
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <form id="formTugboat" class="form-kapal">
                            @csrf
                            <input type="hidden" name="jenis_kapal_id" value="1">
                            <div class="card-body text-center p-4">
                                <div class="rounded-circle mx-auto mb-4 d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px; background: linear-gradient(135deg, #1a2980, #26d0ce);">
                                    <i class="fas fa-ship text-white fa-3x"></i>
                                </div>
                                <h4 class="mb-4">Tugboat</h4>
                                
                                <div class="form-floating mb-4">
                                    <input type="text" class="form-control" name="nama" id="namaTugboat" placeholder="Nama Kapal" required>
                                    <label for="namaTugboat">Nama Kapal</label>
                                </div>

                                <button type="submit" class="btn btn-primary w-100" 
                                        style="background: linear-gradient(135deg, #1a2980, #26d0ce); border: none;">
                                    <i class="fas fa-plus-circle me-2"></i>Tambah Tugboat
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <form id="formTongkang" class="form-kapal">
                            @csrf
                            <input type="hidden" name="jenis_kapal_id" value="2">
                            <div class="card-body text-center p-4">
                                <div class="rounded-circle mx-auto mb-4 d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff416c, #ff4b2b);">
                                    <i class="fas fa-anchor text-white fa-3x"></i>
                                </div>
                                <h4 class="mb-4">Tongkang</h4>
                                
                                <div class="form-floating mb-4">
                                    <input type="text" class="form-control" name="nama" id="namaTongkang" placeholder="Nama Kapal" required>
                                    <label for="namaTongkang">Nama Kapal</label>
                                </div>

                                <button type="submit" class="btn btn-primary w-100" 
                                        style="background: linear-gradient(135deg, #ff416c, #ff4b2b); border: none;">
                                    <i class="fas fa-plus-circle me-2"></i>Tambah Tongkang
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Replace existing Tugboat Modal with this -->
<div class="modal fade" id="tugboatModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-ship me-2"></i>
                    Detail Tugboat
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                @if($tugboats->count() > 0)
                    <div class="row g-3">
                        @foreach($tugboats->sortByDesc('created_at') as $tugboat)
                            <div class="col-12">
                                <div class="kapal-card">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="kapal-header">
                                            <div class="kapal-icon">
                                                <i class="fas fa-ship text-primary"></i>
                                            </div>
                                            <h6 class="kapal-title" id="kapal-nama-{{$tugboat->id}}">
                                                {{ $tugboat->nama }}
                                            </h6>
                                        </div>
                                        <div class="kapal-actions">
                                            <button class="btn btn-sm btn-outline-primary edit-kapal" 
                                                    data-id="{{ $tugboat->id }}" 
                                                    data-nama="{{ $tugboat->nama }}"
                                                    data-jenis-kapal-id="1"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Edit Data Kapal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-kapal" 
                                                    data-id="{{ $tugboat->id }}"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Hapus Kapal">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Informasi Detail Tugboat -->
                                    <div class="detail-info mb-3">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-ship"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">GT/NT</span>
                                                        <span class="detail-value" data-field="gt_nt">{{ $tugboat->gt_nt ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-broadcast-tower"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Call Sign</span>
                                                        <span class="detail-value" data-field="call_sign">{{ $tugboat->call_sign ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Port of Registry</span>
                                                        <span class="detail-value" data-field="port_of_registry">{{ $tugboat->port_of_registry ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-id-card"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Tanda Pendaftaran</span>
                                                        <span class="detail-value" data-field="tanda_pendaftaran">{{ $tugboat->tanda_pendaftaran ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-fingerprint"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">IMO Number</span>
                                                        <span class="detail-value" data-field="imo">{{ $tugboat->imo ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-cogs"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Mesin</span>
                                                        <span class="detail-value" data-field="mesin">{{ $tugboat->mesin ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-certificate"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Tanda Selar</span>
                                                        <span class="detail-value" data-field="tanda_selar">{{ $tugboat->tanda_selar ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-tachometer-alt"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Running Hour Mesin Port Side</span>
                                                        <span class="detail-value" data-field="rh_me_ps">{{ $tugboat->rh_me_ps ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-tachometer-alt"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Running Hour Mesin Starboard Side</span>
                                                        <span class="detail-value" data-field="rh_me_sb">{{ $tugboat->rh_me_sb ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Menu Cards -->
                                    <div class="menu-cards">
                                        <a href="/dokumen-kapal/view/{{ \App\Helpers\HashIdHelper::encode($tugboat->id) }}"
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon">
                                                <i class="fas fa-folder-open"></i>
                                            </div>
                                            <div class="menu-card-title">Dokumen</div>
                                        </a>

                                        <a href="{{ route('checklist-engine.index', ['id' => \App\Helpers\HashIdHelper::encode($tugboat->id)]) }}"
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon engine-icon">
                                                <i class="fas fa-cogs"></i>
                                            </div>
                                            <div class="menu-card-title">Checklist Engine</div>
                                        </a>
                                        
                                        <a href="/checklist-jangkar/{{ \App\Helpers\HashIdHelper::encode($tugboat->id) }}" 
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon anchor-icon">
                                                <i class="fas fa-anchor"></i>
                                            </div>
                                            <div class="menu-card-title">Checklist Perawatan Mesin Jangkar</div>
                                        </a>

                                        <a href="{{ route('penerimaan-kapal.index', ['kapal' => $tugboat->encoded_id]) }}" 
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon">
                                                <i class="fas fa-tools"></i>
                                            </div>
                                            <div class="menu-card-title">Penerimaan Sparepart & Amprahan</div>
                                        </a>

                                        <a href="{{ route('pemakaian-kapal.index', ['kapal' => $tugboat->encoded_id]) }}" 
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon">
                                                <i class="fas fa-tools"></i>
                                            </div>
                                            <div class="menu-card-title">Pemakaian Sparepart & Amprahan</div>
                                        </a>

                                        <a href="{{ route('stok-sparepart.export-form') }}" 
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon" style="background: linear-gradient(135deg, #22c55e, #16a34a);">
                                                <i class="fas fa-file-excel"></i>
                                            </div>
                                            <div class="menu-card-title">Export Excel Stok Sparepart & Amprahan</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="fas fa-ship"></i>
                        <p>Belum ada tugboat yang terdaftar</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Replace existing Tongkang Modal with this -->
<div class="modal fade" id="tongkangModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-anchor me-2"></i>
                    Detail Tongkang
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                @if($tongkangs->count() > 0)
                    <div class="row g-3">
                        @foreach($tongkangs->sortByDesc('created_at') as $tongkang)
                            <div class="col-12">
                                <div class="kapal-card">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="kapal-header">
                                            <div class="kapal-icon">
                                                <i class="fas fa-anchor text-danger"></i>
                                            </div>
                                            <h6 class="kapal-title" id="kapal-nama-{{$tongkang->id}}">
                                                {{ $tongkang->nama }}
                                            </h6>
                                        </div>
                                        <div class="kapal-actions">
                                            <button class="btn btn-sm btn-outline-primary edit-kapal" 
                                                    data-id="{{ $tongkang->id }}" 
                                                    data-nama="{{ $tongkang->nama }}"
                                                    data-jenis-kapal-id="2"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Edit Data Kapal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-kapal" 
                                                    data-id="{{ $tongkang->id }}"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Hapus Kapal">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- Informasi Detail Tongkang -->
                                    <div class="detail-info mb-3">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-ship"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">GT/NT</span>
                                                        <span class="detail-value" data-field="gt_nt">{{ $tongkang->gt_nt ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Port of Registry</span>
                                                        <span class="detail-value" data-field="port_of_registry">{{ $tongkang->port_of_registry ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="detail-item">
                                                    <div class="detail-icon">
                                                        <i class="fas fa-id-card"></i>
                                                    </div>
                                                    <div class="detail-content">
                                                        <span class="detail-label">Tanda Pendaftaran</span>
                                                        <span class="detail-value" data-field="tanda_pendaftaran">{{ $tongkang->tanda_pendaftaran ?: '-' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Menu Cards untuk Tongkang -->
                                    <div class="menu-cards">
                                        <a href="/dokumen-kapal/view/{{ \App\Helpers\HashIdHelper::encode($tongkang->id) }}"
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon">
                                                <i class="fas fa-folder-open"></i>
                                            </div>
                                            <div class="menu-card-title">Dokumen</div>
                                        </a>

                                        <a href="/checklist-jangkar/{{ \App\Helpers\HashIdHelper::encode($tongkang->id) }}"
                                           class="menu-card text-decoration-none">
                                            <div class="menu-card-icon anchor-icon">
                                                <i class="fas fa-anchor"></i>
                                            </div>
                                            <div class="menu-card-title">Checklist Perawatan Mesin Jangkar</div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="fas fa-anchor"></i>
                        <p>Belum ada tongkang yang terdaftar</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Replace existing Edit Modal -->
<div class="modal fade" id="editKapalModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Data Kapal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editKapalForm" action="javascript:void(0)">
                    <input type="hidden" id="editKapalId">
                    <input type="hidden" id="editJenisKapalId">
                    
                    <div class="row g-3">
                        <div class="col-md-12">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="editKapalNama" placeholder="Nama Kapal" required>
                                <label for="editKapalNama">Nama Kapal <span class="text-danger">*</span></label>
                            </div>
                        </div>

                        <!-- Kolom untuk semua jenis kapal -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="editGtNt" placeholder="GT/NT" required>
                                <label for="editGtNt">GT/NT <span class="text-danger">*</span></label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="editPortOfRegistry" placeholder="Port of Registry" required>
                                <label for="editPortOfRegistry">Port of Registry <span class="text-danger">*</span></label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="editTandaPendaftaran" placeholder="Tanda Pendaftaran" required>
                                <label for="editTandaPendaftaran">Tanda Pendaftaran <span class="text-danger">*</span></label>
                            </div>
                        </div>

                        <!-- Kolom khusus untuk tugboat -->
                        <div id="tugboatFields" class="row g-3" style="display: none;">
                            <div class="col-12">
                                <hr>
                                <h6 class="mb-3">Data Tambahan Tugboat</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control tugboat-required" id="editCallSign" placeholder="Call Sign">
                                    <label for="editCallSign">Call Sign <span class="text-danger">*</span></label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control tugboat-required" id="editImo" placeholder="IMO Number">
                                    <label for="editImo">IMO Number <span class="text-danger">*</span></label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control tugboat-required" id="editMesin" placeholder="Mesin">
                                    <label for="editMesin">Mesin <span class="text-danger">*</span></label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control tugboat-required" id="editTandaSelar" placeholder="Tanda Selar">
                                    <label for="editTandaSelar">Tanda Selar <span class="text-danger">*</span></label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control tugboat-required" id="editRhMePs" placeholder="Running Hour Mesin Port Side" min="0" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                    <label for="editRhMePs">Running Hour Mesin Port Side <span class="text-danger">*</span></label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control tugboat-required" id="editRhMeSb" placeholder="Running Hour Mesin Starboard Side" min="0" onkeypress="return event.charCode >= 48 && event.charCode <= 57">
                                    <label for="editRhMeSb">Running Hour Mesin Starboard Side <span class="text-danger">*</span></label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-4">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
.dashboard-wrapper {
    padding: 2rem;
    background: #f8f9fa;
}

.vessel-card {
    position: relative;
    padding: 2rem;
    border-radius: 1.5rem;
    background: white;
    min-height: 300px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.vessel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.vessel-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.tugboat-card .vessel-icon {
    color: #2C73D2;
}

.tongkang-card .vessel-icon {
    color: #FF6B6B;
}

.vessel-info {
    position: relative;
    z-index: 2;
}

.vessel-info h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #2C73D2, #845EC2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tongkang-card .vessel-info h2 {
    background: linear-gradient(45deg, #FF6B6B, #FF9671);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.vessel-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem;
    border-radius: 0.75rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-item-wide {
    grid-column: span 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1rem;
    min-height: 120px; /* Tambahkan minimum height */
}

.checklist-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 0.5rem;
    gap: 0.25rem;
    min-height: 80px; /* Tambahkan minimum height */
    justify-content: center; /* Tengahkan konten secara vertikal */
}

.empty-checklist {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    margin-top: 0.5rem;
}

.no-data {
    color: #6B7280;
    font-size: 0.875rem;
    font-style: italic;
    padding: 1rem;
}

.stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.stat-sublabel {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.separator {
    margin: 0 0.5rem;
    opacity: 0.5;
}

.text-success {
    color: #10B981;
}

.text-danger {
    color: #EF4444;
}

.btn-detail {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 1rem;
    border: none;
    background: linear-gradient(45deg, #2C73D2, #845EC2);
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tongkang-card .btn-detail {
    background: linear-gradient(45deg, #FF6B6B, #FF9671);
}

.btn-detail:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(44, 115, 210, 0.2);
}

.tongkang-card .btn-detail:hover {
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.2);
}

.vessel-backdrop {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    transition: all 0.3s ease;
}

.tugboat-backdrop {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path fill="%232C73D2" d="M20,50 C20,20 50,20 50,50 C50,80 80,80 80,50"></path></svg>');
}

.tongkang-backdrop {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path fill="%23FF6B6B" d="M20,50 C20,20 50,20 50,50 C50,80 80,80 80,50"></path></svg>');
}

.vessel-card:hover .vessel-backdrop {
    opacity: 0.1;
    transform: scale(1.1);
}

/* ...existing styles... */

.btn-modern-outline {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid #2C73D2;
    background: transparent;
    color: #2C73D2;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-modern-outline:hover {
    background: linear-gradient(135deg, #2C73D2, #845EC2);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 115, 210, 0.2);
}

/* Update existing button styles */
.btn-modern {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: none;
    background: linear-gradient(135deg, #2C73D2, #845EC2);
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 115, 210, 0.2);
}

/* Elegant Modal Styles */
.modal-content {
    border: none;
    border-radius: 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #1a2980, #26d0ce);
    padding: 1.5rem;
    border: none;
}

.modal-header .modal-title {
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
}

.modal-header .btn-close {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    padding: 0.75rem;
    margin: -0.75rem;
    transition: transform 0.3s ease;
}

.modal-header .btn-close:hover {
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
}

/* Document List Styles */
.dokumen-list {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px dashed #cbd5e1;
    transition: all 0.3s ease;
}

.dokumen-list:hover {
    border-color: #2C73D2;
    box-shadow: 0 4px 15px rgba(44, 115, 210, 0.1);
}

/* Upload Form Styles */
.upload-dokumen-form {
    position: relative;
    background: white;
    padding: 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.upload-dokumen-form::before {
    content: '📄 Upload Dokumen Baru';
    display: block;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.upload-dokumen-form .input-group {
    background: #f1f5f9;
    padding: 0.5rem;
    border-radius: 0.75rem;
    border: 2px dashed #cbd5e1;
}

.upload-dokumen-form input[type="file"] {
    border: none;
    background: transparent;
    padding: 0.5rem;
    color: #64748b;
}

.upload-dokumen-form .btn-primary {
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.upload-dokumen-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 115, 210, 0.2);
}

/* Document Item Styles */
.dokumen-item {
    background: white;
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.75rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.dokumen-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dokumen-item a {
    color: #1e293b;
    font-weight: 500;
    text-decoration: none;
}

.dokumen-item a:hover {
    color: #2C73D2;
}

.dokumen-item .delete-dokumen {
    opacity: 0;
    transition: all 0.3s ease;
}

.dokumen-item:hover .delete-dokumen {
    opacity: 1;
}

/* File Upload Info */
.file-upload-info {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border-left: 4px solid #2C73D2;
}

.file-upload-info i {
    color: #2C73D2;
    margin-right: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #64748b;
}

.empty-state i {
    font-size: 3rem;
    color: #cbd5e1;
    margin-bottom: 1rem;
}

.kapal-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.kapal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.kapal-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.kapal-icon {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f1f5f9;
    font-size: 1.5rem;
}

.kapal-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
}

.kapal-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.kapal-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.dokumen-list {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

/* Menu Cards Styles */
.menu-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
}

.menu-card {
    flex: 1 1 calc(20% - 0.75rem); /* 5 cards per row pada desktop */
    min-width: 100px;
    max-width: 150px;
    background: white;
    border-radius: 0.75rem;
    padding: 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Responsive breakpoints untuk menu cards */
@media (max-width: 1200px) {
    .menu-card {
        flex: 1 1 calc(25% - 0.75rem); /* 4 cards per row */
    }
}

@media (max-width: 992px) {
    .menu-card {
        flex: 1 1 calc(33.333% - 0.75rem); /* 3 cards per row */
    }
}

@media (max-width: 768px) {
    .menu-card {
        flex: 1 1 calc(50% - 0.75rem); /* 2 cards per row */
    }
    
    .menu-cards {
        gap: 0.5rem;
    }
}

@media (max-width: 576px) {
    .menu-card {
        flex: 1 1 100%; /* 1 card per row */
        max-width: 100%;
    }
    
    .menu-cards {
        flex-direction: column;
    }
    
    .menu-card-title {
        font-size: 0.85rem;
    }
}

.menu-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-card-icon {
    width: 35px;
    height: 35px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.menu-card:hover .menu-card-icon {
    transform: scale(1.1);
}

.menu-card-title {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.75rem;
    line-height: 1.2;
    margin-top: 0.25rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Perbaikan container untuk menu cards */
.kapal-card {
    overflow: hidden;
}

.kapal-card .menu-cards {
    margin: -0.375rem;
    padding: 0.375rem;
    width: 100%;
    box-sizing: border-box;
}

.menu-card-icon.engine-icon {
    background: linear-gradient(135deg, #FF6B6B, #FF9671);
}

.menu-card-icon.anchor-icon {
    background: linear-gradient(135deg, #845EC2, #B39CD0);
}

.menu-card:hover .menu-card-icon {
    transform: scale(1.1);
}

.menu-card-title {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.75rem;
    line-height: 1.2;
    margin-top: 0.25rem;
}

.menu-card:hover .menu-card-title {
    color: #2C73D2;
}

/* Tambahkan style yang sama untuk modal tongkang */
#tongkangModal .menu-card-icon {
    background: linear-gradient(135deg, #FF6B6B, #FF9671);
}

#tongkangModal .menu-card:hover {
    border-color: #FF6B6B;
}

#tongkangModal .menu-card:hover .menu-card-title {
    color: #FF6B6B;
}

/* Style untuk menu cards di modal tugboat */
#tugboatModal .menu-card-icon {
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
}

#tugboatModal .menu-card-icon.engine-icon {
    background: linear-gradient(135deg, #45a6e5, #0093E9);
}

#tugboatModal .menu-card-icon.anchor-icon {
    background: linear-gradient(135deg, #0093E9, #80D0C7);
}

#tugboatModal .menu-card:hover {
    border-color: #2C73D2;
}

#tugboatModal .menu-card:hover .menu-card-title {
    color: #2C73D2;
}

/* Shared menu card styles */
.menu-cards {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
}

.menu-card {
    background: white;
    border-radius: 0.75rem;
    padding: 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    flex: 1;
    min-width: 80px;
    max-width: 100px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.menu-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-card-icon {
    width: 35px;
    height: 35px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.menu-card:hover .menu-card-icon {
    transform: scale(1.1);
}

.menu-card-title {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.75rem;
    line-height: 1.2;
    margin-top: 0.25rem;
}

/* Perbaikan style untuk form floating */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
    line-height: 1.25;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 1rem 0.75rem;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
    color: #6c757d;
    z-index: 3;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .85;
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
    background-color: white;
    height: auto;
    padding: 0 0.5rem;
    margin-left: 0.5rem;
}

.form-floating > .form-control:focus {
    border-color: #1a2980;
    box-shadow: 0 0 0 0.25rem rgba(26, 41, 128, 0.25);
}

.form-floating > .form-control::placeholder {
    color: transparent;
}

/* Style untuk card form */
.card {
    border-radius: 1rem;
    overflow: hidden;
}

.card-body {
    background: linear-gradient(to bottom, #ffffff, #f8f9fa);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(26, 41, 128, 0.3);
}

/* Style untuk detail info */
.detail-info {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.detail-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detail-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

#tongkangModal .detail-icon {
    background: linear-gradient(135deg, #FF6B6B, #FF9671);
}

.detail-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.detail-label {
    font-weight: 600;
    color: #64748b;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: 0;
}

.detail-value {
    color: #1e293b;
    font-weight: 500;
    font-size: 0.85rem;
    line-height: 1.2;
}

/* Animasi hover untuk detail items */
.detail-item:hover .detail-icon {
    transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .detail-info {
        padding: 0.75rem;
    }
    
    .detail-item {
        padding: 0.5rem;
    }
    
    .detail-icon {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }
    
    .detail-label {
        font-size: 0.65rem;
    }
    
    .detail-value {
        font-size: 0.8rem;
    }
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.notification-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.notification-icon.bg-warning {
    background: linear-gradient(135deg, #ffa726, #fb8c00);
}

.notification-icon.bg-danger {
    background: linear-gradient(135deg, #ef5350, #e53935);
}

.notification-icon.bg-secondary {
    background: linear-gradient(135deg, #78909c, #546e7a);
}

.notification-content {
    flex-grow: 1;
}

.notification-text {
    margin-bottom: 0.25rem;
    color: #1e293b;
    font-size: 0.9rem;
}

/* Tambahkan style untuk pagination */
.pagination {
    margin: 0;
    gap: 0.5rem;
}

.page-item {
    margin: 0;
}

.page-link {
    border-radius: 0.5rem;
    border: none;
    padding: 0.5rem 1rem;
    color: #1e293b;
    background: #f1f5f9;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: #e2e8f0;
    color: #1e293b;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
    color: white;
}

.page-item.disabled .page-link {
    background: #f1f5f9;
    color: #94a3b8;
}

/* Responsive pagination */
@media (max-width: 576px) {
    .pagination {
        gap: 0.25rem;
    }
    
    .page-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

/* Update style pagination */
.modern-pagination {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin: 0;
    padding: 0;
}

.modern-pagination .page-item {
    margin: 0;
}

.modern-pagination .page-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    border: none;
    font-weight: 500;
    font-size: 0.95rem;
    color: #1e293b;
    background: #f1f5f9;
    transition: all 0.3s ease;
    padding: 0;
    margin: 0;
}

.modern-pagination .page-item:not(.active) .page-link:hover {
    background: #e2e8f0;
    color: #1e293b;
    transform: translateY(-2px);
}

.modern-pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
    color: white;
    box-shadow: 0 4px 15px rgba(44, 115, 210, 0.2);
}

.modern-pagination .page-item.disabled .page-link {
    background: #f1f5f9;
    color: #94a3b8;
    cursor: not-allowed;
    opacity: 0.7;
}

.modern-pagination .page-link i {
    font-size: 0.8rem;
}

/* Responsive pagination */
@media (max-width: 576px) {
    .modern-pagination {
        gap: 0.25rem;
    }
    
    .modern-pagination .page-link {
        width: 35px;
        height: 35px;
        font-size: 0.85rem;
    }
}

/* Dark mode support */
[data-bs-theme="dark"] .modern-pagination .page-link {
    background: #1e293b;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .modern-pagination .page-item:not(.active) .page-link:hover {
    background: #2d3748;
}

[data-bs-theme="dark"] .modern-pagination .page-item.disabled .page-link {
    background: #1e293b;
    color: #4a5568;
}

/* Loading state */
.notification-list.loading {
    position: relative;
    min-height: 200px;
}

.notification-list.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 2rem;
    color: #2C73D2;
    animation: fa-spin 1s infinite linear;
}

.notification-list.loading::after {
    content: '\f1ce'; /* FontAwesome spinner icon */
}

/* Smooth transition for notifications */
.notification-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Warna teks notifikasi */
.notification-text.text-warning {
    color: #fb8c00;
    font-weight: 500;
}

.notification-text.text-danger {
    color: #e53935;
    font-weight: 500;
}

.notification-text.text-secondary {
    color: #546e7a;
    font-weight: 500;
}

/* Dark mode support */
[data-bs-theme="dark"] .notification-text.text-warning {
    color: #ffa726;
}

[data-bs-theme="dark"] .notification-text.text-danger {
    color: #ef5350;
}

[data-bs-theme="dark"] .notification-text.text-secondary {
    color: #78909c;
}

/* Warna untuk Tugboat */
.notification-icon.bg-warning.tugboat,
.notification-icon.bg-danger.tugboat,
.notification-icon.bg-secondary.tugboat {
    background: linear-gradient(135deg, #1a2980, #26d0ce);
}

.notification-text.tugboat {
    color: #1e293b;
    font-weight: 500;
}

/* Warna untuk Tongkang */
.notification-icon.bg-warning.tongkang,
.notification-icon.bg-danger.tongkang,
.notification-icon.bg-secondary.tongkang {
    background: linear-gradient(135deg, #ff416c, #ff4b2b);
}

.notification-text.tongkang {
    color: #1e293b;
    font-weight: 500;
}

/* Dark mode support */
[data-bs-theme="dark"] .notification-text.tugboat,
[data-bs-theme="dark"] .notification-text.tongkang {
    color: #e2e8f0;
}

/* Hover effect untuk item notifikasi */
.notification-item.tugboat {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.notification-item.tongkang {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.notification-item.tugboat:hover {
    border-left-color: #1a2980;
    background: rgba(26, 41, 128, 0.05);
}

.notification-item.tongkang:hover {
    border-left-color: #ff416c;
    background: rgba(255, 65, 108, 0.05);
}

/* Dark mode hover effect */
[data-bs-theme="dark"] .notification-item.tugboat:hover {
    background: rgba(37, 99, 235, 0.15) !important;
    border-left-color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .notification-item.tongkang:hover {
    background: rgba(234, 88, 12, 0.15) !important;
    border-left-color: #ea580c !important;
}

/* Dark mode notification item background */
[data-bs-theme="dark"] .notification-item {
    background: var(--surface-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

/* Status indicators */
.notification-text.text-warning::before,
.notification-text.text-danger::before,
.notification-text.text-secondary::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.notification-text.text-warning::before {
    background: #fb8c00;
}

.notification-text.text-danger::before {
    background: #e53935;
}

.notification-text.text-secondary::before {
    background: #546e7a;
}

/* Warna nama kapal */
.kapal-name.tugboat {
    color: #1a2980;
    font-weight: 600;
}

.kapal-name.tongkang {
    color: #ff416c;
    font-weight: 600;
}

/* Dark mode support untuk nama kapal */
[data-bs-theme="dark"] .kapal-name.tugboat {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .kapal-name.tongkang {
    color: #ea580c !important;
}

/* Notification text normal color */
.notification-text {
    color: var(--text-primary, #1e293b);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

[data-bs-theme="dark"] .notification-text {
    color: var(--text-primary) !important;
}

/* Expiry alert styling */
.expiry-alert {
    color: #fb8c00;
    font-weight: 500;
}

.expiry-alert strong {
    color: #e65100;
    font-weight: 600;
}

.days-left {
    background: rgba(251, 140, 0, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    color: #fb8c00;
    margin-left: 4px;
    white-space: nowrap;
}

/* Dark mode support */
[data-bs-theme="dark"] .expiry-alert {
    color: var(--warning-color) !important;
}

[data-bs-theme="dark"] .expiry-alert strong {
    color: #f59e0b !important;
}

[data-bs-theme="dark"] .days-left {
    background: rgba(210, 153, 34, 0.15) !important;
    color: var(--warning-color) !important;
}

/* Notification toggle button */
.btn-modern-outline {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: transparent;
    color: #64748b;
    transition: all 0.3s ease;
}

.btn-modern-outline:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #1e293b;
    transform: translateY(-1px);
}

.btn-modern-outline i {
    transition: transform 0.3s ease;
}

.btn-modern-outline.collapsed i {
    transform: rotate(180deg);
}

/* Notification badge */
.notification-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    background: #ef4444;
    color: white;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 8px;
}

/* Dark mode support */
[data-bs-theme="dark"] .btn-modern-outline {
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
    background: var(--surface-card) !important;
}

[data-bs-theme="dark"] .btn-modern-outline:hover {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    border-color: var(--primary-color) !important;
}

/* Styling untuk tombol Tambah Kapal yang baru */
.add-ship-btn {
    padding: 0.85rem 1.75rem;
    border-radius: 15px;
    border: none;
    background: linear-gradient(135deg, #2C73D2, #45a6e5);
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(44, 115, 210, 0.15);
    position: relative;
    overflow: hidden;
    z-index: 1;
    min-width: 200px;
}

.add-ship-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a2980, #26d0ce);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.add-ship-btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
    color: white;
}

.add-ship-btn:hover {
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(44, 115, 210, 0.25);
}

.add-ship-btn:hover::before {
    opacity: 1;
}

.add-ship-btn:hover i {
    transform: rotate(90deg);
    color: #ffffff;
}

.add-ship-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(44, 115, 210, 0.2);
}

/* Dark mode support */
[data-bs-theme="dark"] .add-ship-btn {
    background: linear-gradient(135deg, #45a6e5, #26d0ce);
    color: #ffffff;
}

[data-bs-theme="dark"] .add-ship-btn:hover {
    color: #ffffff;
}

[data-bs-theme="dark"] .add-ship-btn::before {
    background: linear-gradient(135deg, #26d0ce, #1a2980);
}

/* Animasi ripple effect */
.add-ship-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, .5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.add-ship-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

.add-ship-btn .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    z-index: 2;
}

.add-ship-btn .btn-text {
    font-weight: 600;
    letter-spacing: 0.3px;
    white-space: nowrap;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .add-ship-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
        min-width: auto;
    }
    
    .add-ship-btn .btn-text {
        display: none;
    }
    
    .add-ship-btn .btn-content {
        gap: 0;
    }
    
    .add-ship-btn i {
        margin: 0;
    }
}

.last-check {
    font-size: 0.75rem;
    color: #6B7280;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.last-check i {
    font-size: 0.8rem;
    color: #8B5CF6;
}

.no-data {
    color: #6B7280;
    font-size: 0.875rem;
    font-style: italic;
}

/* Style untuk daftar item rusak */
.rusak-items {
    list-style: none;
    padding-left: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

.rusak-items li {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.5rem;
    background: rgba(239, 68, 68, 0.05);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.rusak-items li:last-child {
    margin-bottom: 0;
}

.item-name {
    font-weight: 600;
    color: #ef4444;
}

.item-details {
    display: flex;
    gap: 1rem;
    font-size: 0.85em;
    color: #6b7280;
}

.jam, .keterangan {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.jam::before {
    content: '\f017';
    font-family: "Font Awesome 5 Free";
    font-weight: 400;
}

.keterangan::before {
    content: '\f05a';
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}

/* Dark mode support */
[data-bs-theme="dark"] .rusak-items li {
    background: rgba(239, 68, 68, 0.1);
}

[data-bs-theme="dark"] .item-name {
    color: #f87171;
}

[data-bs-theme="dark"] .item-details {
    color: #9ca3af;
}

/* Style untuk Menu Card Large */
.menu-card-large {
    display: flex;
    align-items: center;
    padding: 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.menu-card-large:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    text-decoration: none;
    color: inherit;
}

.menu-card-icon-lg {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1rem;
    margin-right: 1.5rem;
    flex-shrink: 0;
    font-size: 1.75rem;
    position: relative;
    z-index: 1;
}

.menu-card-large:nth-child(odd) .menu-card-icon-lg {
    background: linear-gradient(135deg, #1a2980, #26d0ce);
    color: white;
}

.menu-card-large:nth-child(even) .menu-card-icon-lg {
    background: linear-gradient(135deg, #ff416c, #ff4b2b);
    color: white;
}

.menu-card-content {
    flex-grow: 1;
    position: relative;
    z-index: 1;
}

.menu-card-title-lg {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.menu-card-desc {
    color: #64748b;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.menu-card-arrow {
    margin-left: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f1f5f9;
    color: #64748b;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.menu-card-large:hover .menu-card-arrow {
    background: #1e293b;
    color: white;
    transform: translateX(5px);
}

.menu-card-large::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 41, 128, 0.05), rgba(38, 208, 206, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-card-large:nth-child(even)::after {
    background: linear-gradient(135deg, rgba(255, 65, 108, 0.05), rgba(255, 75, 43, 0.05));
}

.menu-card-large:hover::after {
    opacity: 1;
}

/* Dark mode support */
[data-bs-theme="dark"] .menu-card-large {
    background: #1e293b;
}

[data-bs-theme="dark"] .menu-card-title-lg {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .menu-card-desc {
    color: #94a3b8;
}

[data-bs-theme="dark"] .menu-card-arrow {
    background: #2d3748;
    color: #e2e8f0;
}

[data-bs-theme="dark"] .menu-card-large:hover .menu-card-arrow {
    background: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .menu-card-large {
        padding: 1.5rem;
    }
    
    .menu-card-icon-lg {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-right: 1rem;
    }
    
    .menu-card-title-lg {
        font-size: 1.25rem;
    }
    
    .menu-card-desc {
        font-size: 0.875rem;
    }
    
    .menu-card-arrow {
        width: 35px;
        height: 35px;
        margin-left: 1rem;
    }
}

@media (max-width: 576px) {
    .menu-card-desc {
        display: none;
    }
}

/* Style untuk notifikasi pengeluaran kantor */
.notification-icon.bg-info {
    background: linear-gradient(135deg, #0ea5e9, #38bdf8);
}

.notification-text.info {
    color: #0ea5e9;
    font-weight: 500;
}

/* Dark mode support */
[data-bs-theme="dark"] .notification-text.info {
    color: #38bdf8;
}

/* Status indicators untuk pengeluaran kantor */
.notification-text.text-info::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    background: #0ea5e9;
}

/* Style untuk notifikasi stok kapal */
.notification-text strong {
    color: #2d3748;
    font-weight: 600;
}

[data-bs-theme="dark"] .notification-text strong {
    color: #e2e8f0;
}

.notification-item.warning {
    border-left: 4px solid #f59e0b;
    background: rgba(245, 158, 11, 0.05);
}

[data-bs-theme="dark"] .notification-item.warning {
    background: rgba(245, 158, 11, 0.15);
}

.notification-icon.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

/* Style untuk notifikasi stok kapal habis */
.notification-item.stok-kapal-habis {
    border-left: 4px solid #eab308;
    background: rgba(234, 179, 8, 0.05);
}

[data-bs-theme="dark"] .notification-item.stok-kapal-habis {
    background: rgba(234, 179, 8, 0.15);
}

.notification-icon.stok-kapal-habis {
    background: linear-gradient(135deg, #eab308, #fbbf24);
}

/* Tambahkan style untuk group title */
.notification-group-title.stok-kapal-habis {
    color: #eab308;
    font-weight: 600;
}

[data-bs-theme="dark"] .notification-group-title.stok-kapal-habis {
    color: #fbbf24;
}

/* Update style untuk notifikasi */
.notification-text {
    color: #1a202c;  /* Warna teks lebih gelap */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 0.95rem;
    line-height: 1.5;
    font-weight: 500;
}

.notification-text strong {
    color: #1a202c;  /* Warna teks bold lebih gelap */
    font-weight: 600;
}

/* Style untuk notifikasi stok kapal habis */
.notification-item.stok-kapal-habis {
    border-left: 4px solid #d97706;  /* Warna border lebih gelap */
    background: rgba(217, 119, 6, 0.08);  /* Background lebih gelap sedikit */
    padding: 1.25rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

[data-bs-theme="dark"] .notification-item.stok-kapal-habis {
    background: rgba(217, 119, 6, 0.2);  /* Background dark mode lebih gelap */
}

.notification-icon.stok-kapal-habis {
    background: linear-gradient(135deg, #b45309, #d97706);  /* Gradient lebih gelap */
}

/* Group title untuk stok kapal habis */
.notification-group-title.stok-kapal-habis {
    color: #b45309;  /* Warna judul lebih gelap */
    font-weight: 600;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

[data-bs-theme="dark"] .notification-group-title.stok-kapal-habis {
    color: #fbbf24;
}

/* Nama kapal dalam notifikasi */
.kapal-name {
    font-weight: 600;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.kapal-name.tugboat {
    color: #1e40af;  /* Biru lebih gelap */
}

.kapal-name.tongkang {
    color: #be123c;  /* Merah lebih gelap */
}

/* Style untuk detail informasi */
.notification-details {
    color: #4b5563;  /* Abu-abu lebih gelap */
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Style untuk badge/label */
.notification-badge {
    background: rgba(217, 119, 6, 0.1);
    color: #b45309;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

/* Style untuk icon */
.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    font-size: 1.1rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

/* Dark mode adjustments */
[data-bs-theme="dark"] .notification-text {
    color: #e2e8f0;
}

[data-bs-theme="dark"] .notification-text strong {
    color: #f8fafc;
}

[data-bs-theme="dark"] .kapal-name.tugboat {
    color: #60a5fa;
}

[data-bs-theme="dark"] .kapal-name.tongkang {
    color: #fb7185;
}

[data-bs-theme="dark"] .notification-details {
    color: #94a3b8;
}

/* Hover effects */
.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Font imports - tambahkan di bagian atas file */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Base styles untuk semua notifikasi */
.notification-item {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    letter-spacing: -0.01em;
}

/* Style untuk informasi tambahan */
.info-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
}

[data-bs-theme="dark"] .info-text {
    color: #94a3b8;
}

/* Style untuk angka/jumlah */
.number-highlight {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 600;
    color: #1e40af;
    background: rgba(30, 64, 175, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 0.375rem;
}

[data-bs-theme="dark"] .number-highlight {
    color: #60a5fa;
    background: rgba(96, 165, 250, 0.1);
}

.add-ship-btn:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
    border-radius: inherit;
    z-index: -1;
    transition: all 0.3s ease;
}

.add-ship-btn:hover:after {
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
}

.add-ship-btn i {
    transition: transform 0.3s ease;
}

.add-ship-btn:hover i {
    transform: translateX(3px);
}

.add-ship-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(44, 115, 210, 0.2);
}

/* ========== ENHANCED DARK MODE FOR DASHBOARD ========== */

/* Main layout */
[data-bs-theme="dark"] .dashboard-wrapper {
    background-color: var(--bg-primary) !important;
}

[data-bs-theme="dark"] .header-section {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
}

/* Statistics cards */
[data-bs-theme="dark"] .stats-card {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .stats-card:hover {
    background: var(--bg-tertiary) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 4px 12px var(--shadow-sm) !important;
}

[data-bs-theme="dark"] .stats-card .card-body {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .stats-card h3 {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .stats-card .text-muted {
    color: var(--text-secondary) !important;
}

/* Kapal cards */
[data-bs-theme="dark"] .kapal-card {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 6px var(--shadow-sm) !important;
}

[data-bs-theme="dark"] .kapal-card:hover {
    background: var(--bg-tertiary) !important;
    box-shadow: 0 10px 15px var(--shadow-md) !important;
    border-color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .kapal-title {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .kapal-icon {
    background: var(--bg-tertiary) !important;
    color: var(--text-secondary) !important;
}

/* Menu cards */
[data-bs-theme="dark"] .menu-card {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .menu-card:hover {
    background: var(--bg-tertiary) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-primary) !important;
    text-decoration: none !important;
}

[data-bs-theme="dark"] .menu-card-icon {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .menu-card:hover .menu-card-icon {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .menu-card-title {
    color: var(--text-primary) !important;
}

/* Engine icon specific */
[data-bs-theme="dark"] .engine-icon {
    color: var(--warning-color) !important;
}

[data-bs-theme="dark"] .anchor-icon {
    color: var(--info-color) !important;
}

/* Add ship button dark mode */
[data-bs-theme="dark"] .add-ship-btn {
    background: linear-gradient(135deg, var(--primary-color), #409eff) !important;
    border: 1px solid var(--primary-color) !important;
    box-shadow: 0 4px 15px rgba(88, 166, 255, 0.15) !important;
}

[data-bs-theme="dark"] .add-ship-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color)) !important;
    box-shadow: 0 6px 20px rgba(88, 166, 255, 0.25) !important;
}

/* Notification badge dark mode */
[data-bs-theme="dark"] .notification-badge {
    background: var(--danger-color) !important;
    color: #ffffff !important;
}

/* Status indicators dark mode */
[data-bs-theme="dark"] .notification-text.text-warning::before {
    background: var(--warning-color) !important;
}

[data-bs-theme="dark"] .notification-text.text-danger::before {
    background: var(--danger-color) !important;
}

[data-bs-theme="dark"] .notification-text.text-secondary::before {
    background: var(--text-secondary) !important;
}

/* Modal improvements for dashboard */
[data-bs-theme="dark"] .modal-content {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
}

[data-bs-theme="dark"] .modal-backdrop {
    background-color: rgba(13, 17, 23, 0.8) !important;
}

/* Table improvements */
[data-bs-theme="dark"] .table th {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table td {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

/* Breadcrumb */
[data-bs-theme="dark"] .breadcrumb {
    background: var(--surface-card) !important;
}

[data-bs-theme="dark"] .breadcrumb-item {
    color: var(--text-secondary) !important;
}

[data-bs-theme="dark"] .breadcrumb-item.active {
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted) !important;
}

/* Progress bars */
[data-bs-theme="dark"] .progress {
    background-color: var(--bg-tertiary) !important;
}

[data-bs-theme="dark"] .progress-bar {
    background-color: var(--primary-color) !important;
}

/* Nav tabs */
[data-bs-theme="dark"] .nav-tabs {
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link {
    color: var(--text-secondary) !important;
    border-color: transparent !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link:hover {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    color: var(--text-primary) !important;
    background-color: var(--surface-card) !important;
    border-color: var(--border-color) !important;
}

/* Tab content */
[data-bs-theme="dark"] .tab-content {
    background: var(--surface-card) !important;
    border: 1px solid var(--border-color) !important;
}

/* Accordion */
[data-bs-theme="dark"] .accordion-item {
    background: var(--surface-card) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .accordion-header .accordion-button {
    background: var(--surface-card) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .accordion-button:not(.collapsed) {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .accordion-body {
    background: var(--surface-card) !important;
    color: var(--text-primary) !important;
}

/* Pagination */
[data-bs-theme="dark"] .pagination .page-link {
    background: var(--surface-card) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .pagination .page-link:hover {
    background: var(--bg-tertiary) !important;
    border-color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .pagination .page-item.active .page-link {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Tooltip */
[data-bs-theme="dark"] .tooltip .tooltip-inner {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

[data-bs-theme="dark"] .tooltip .tooltip-arrow::before {
    border-top-color: var(--border-color) !important;
}

/* Popover */
[data-bs-theme="dark"] .popover {
    background: var(--surface-card) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .popover-header {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

[data-bs-theme="dark"] .popover-body {
    color: var(--text-primary) !important;
}

/* Scrollbar styling for dark mode */
[data-bs-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Selection color */
[data-bs-theme="dark"] ::selection {
    background: rgba(88, 166, 255, 0.3);
    color: var(--text-primary);
}

[data-bs-theme="dark"] ::-moz-selection {
    background: rgba(88, 166, 255, 0.3);
    color: var(--text-primary);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize modals
    window.tugboatModal = new bootstrap.Modal(document.getElementById('tugboatModal'));
    window.tongkangModal = new bootstrap.Modal(document.getElementById('tongkangModal'));
    window.editModal = new bootstrap.Modal(document.getElementById('editKapalModal'));

    // Global modal show functions
    window.showTugboatModal = function() {
        window.tugboatModal.show();
    };

    window.showTongkangModal = function() {
        window.tongkangModal.show();
    };

    // Toggle form visibility
    $('#toggleKapal').click(function() {
        $('#formKapal').slideToggle();
    });

    // Initialize event handlers
    function initializeHandlers() {
        // Edit button handler
        $('.edit-kapal').off('click').on('click', function(e) {
            e.stopPropagation();
            const id = $(this).data('id');
            const nama = $(this).data('nama');
            const jenisKapalId = $(this).data('jenis-kapal-id');
            
            // Show/hide tugboat specific fields
            if (jenisKapalId == 1) {
                $('#tugboatFields').show();
                $('.tugboat-required').prop('required', true);
            } else {
                $('#tugboatFields').hide();
                $('.tugboat-required').prop('required', false);
            }
            
            // Fetch kapal data
            $.get(`/kapal/${id}`, function(response) {
                if (response.success) {
                    const kapal = response.data;
                    
                    // Update form fields
                    $('#editKapalId').val(kapal.id);
                    $('#editJenisKapalId').val(kapal.jenis_kapal_id);
                    $('#editKapalNama').val(kapal.nama);
                    $('#editGtNt').val(kapal.gt_nt);
                    $('#editPortOfRegistry').val(kapal.port_of_registry);
                    $('#editTandaPendaftaran').val(kapal.tanda_pendaftaran);
                    
                    // Update tugboat specific fields
                    if (kapal.jenis_kapal_id == 1) {
                        $('#editCallSign').val(kapal.call_sign);
                        $('#editImo').val(kapal.imo);
                        $('#editMesin').val(kapal.mesin);
                        $('#editTandaSelar').val(kapal.tanda_selar);
                        $('#editRhMePs').val(kapal.rh_me_ps);
                        $('#editRhMeSb').val(kapal.rh_me_sb);
                    }
                    
                    window.editModal.show();
                }
            });
        });

        // Delete button handler
        $('.delete-kapal').off('click').on('click', function(e) {
            e.stopPropagation();
            const id = $(this).data('id');
            const namaKapal = $(`#kapal-nama-${id}`).text().trim();
            const jenisKapal = $(this).closest('.modal-content').find('.modal-title').text().includes('Tugboat') ? 'Tugboat' : 'Tongkang';
            handleDelete(id, jenisKapal, namaKapal);
        });

        // Add document viewer toggle handler
        $('.view-dokumen').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const kapalId = $(this).data('id');
            $(`#dokumen-list-${kapalId}`).slideToggle();
        });

        // Add document upload handler
        $('.upload-dokumen-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const form = $(this);
            const kapalId = form.data('kapal-id');
            const formData = new FormData(this);
            formData.append('kapal_id', kapalId);
            
            // Show loading state
            const submitButton = form.find('button[type="submit"]');
            submitButton.prop('disabled', true);
            submitButton.html('<i class="fas fa-spinner fa-spin"></i>');
            
            // Show loading indicator
            Swal.fire({
                title: 'Mengupload dokumen...',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            $.ajax({
                url: '{{ route("dokumen.store") }}',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        const dokumenContainer = form.closest('.dokumen-list').find('.dokumen-items');
                        const emptyState = dokumenContainer.find('.empty-state');
                        
                        // Remove empty state if it exists
                        if (emptyState.length) {
                            emptyState.remove();
                        }
                        
                        const dokumenHtml = `
                            <div class="dokumen-item" id="dokumen-${response.dokumen.id}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="${response.dokumen.file_url}" target="_blank">
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                        ${response.dokumen.nama_file}
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger delete-dokumen" 
                                            data-id="${response.dokumen.id}"
                                            data-bs-toggle="tooltip"
                                            title="Hapus Dokumen">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        
                        // Append new document with fade in effect
                        const newDokumen = $(dokumenHtml).hide();
                        dokumenContainer.append(newDokumen);
                        newDokumen.fadeIn(300);
                        
                        // Initialize tooltip for new delete button
                        var tooltip = new bootstrap.Tooltip(newDokumen.find('[data-bs-toggle="tooltip"]')[0]);
                        
                        // Clear the file input
                        form.find('input[type="file"]').val('');
                        
                        // Show success message
                        Swal.fire({
                            title: 'Berhasil!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        
                        // Initialize delete handler for new document
                        handleDeleteDokumen();
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Gagal mengupload dokumen';
                    if (xhr.status === 419) {
                        errorMessage = 'Sesi telah berakhir. Silakan muat ulang halaman.';
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        timer: 2000,
                        showConfirmButton: false
                    });
                },
                complete: function() {
                    // Reset submit button
                    submitButton.prop('disabled', false);
                    submitButton.html('<i class="fas fa-upload me-1"></i> Upload');
                }
            });
        });

        // Prevent click events from bubbling up in the document list
        $('.dokumen-list').off('click').on('click', function(e) {
            e.stopPropagation();
        });

        // Panggil handleDeleteDokumen di dalam initializeHandlers
        handleDeleteDokumen();
    }

    // Form submissions
    $('form:not(.logout-form, .upload-dokumen-form)').on('submit', function(e) {
        e.preventDefault();
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const jenisKapalId = form.find('input[name="jenis_kapal_id"]').val();
        const namaKapal = form.find('input[name="nama"]').val();
        
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Menambahkan...');
        
        $.ajax({
            url: form.attr('action'),
            method: form.attr('method') || 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    $('#formKapal').slideUp();
                    form[0].reset();
                    
                    // Update counts from server response
                    $('.tugboat-card .stat-number:first').text(response.counts.tugboat);
                    $('.tongkang-card .stat-number:first').text(response.counts.tongkang);
                    
                    // Add new ship to the corresponding modal
                    const modalSelector = jenisKapalId === '1' ? '#tugboatModal' : '#tongkangModal';
                    const modalBody = $(`${modalSelector} .modal-body`);
                    
                    // Remove empty state if exists
                    modalBody.find('.empty-state').remove();
                    
                    // Create new card HTML for the ship
                    const newShipHtml = `
                        <div class="col-12">
                            <div class="kapal-card">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div class="kapal-header">
                                        <div class="kapal-icon">
                                            <i class="fas ${jenisKapalId === '1' ? 'fa-ship text-primary' : 'fa-anchor text-danger'}"></i>
                                        </div>
                                        <h6 class="kapal-title" id="kapal-nama-${response.kapal.id}">
                                            ${response.kapal.nama}
                                        </h6>
                                    </div>
                                    <div class="kapal-actions">
                                        <button class="btn btn-sm btn-outline-primary edit-kapal" 
                                                data-id="${response.kapal.id}" 
                                                data-nama="${response.kapal.nama}"
                                                data-jenis-kapal-id="${jenisKapalId}"
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="Edit Data Kapal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger delete-kapal" 
                                                data-id="${response.kapal.id}"
                                                data-bs-toggle="tooltip"
                                                data-bs-placement="top"
                                                title="Hapus Kapal">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Informasi Detail Kapal -->
                                <div class="detail-info mb-3">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-ship"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">GT/NT</span>
                                                    <span class="detail-value" data-field="gt_nt">-</span>
                                                </div>
                                            </div>
                                        ${jenisKapalId === '1' ? `
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-broadcast-tower"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Call Sign</span>
                                                    <span class="detail-value" data-field="call_sign">-</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-fingerprint"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">IMO Number</span>
                                                    <span class="detail-value" data-field="imo">-</span>
                                                </div>
                                            </div>
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-cogs"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Mesin</span>
                                                    <span class="detail-value" data-field="mesin">-</span>
                                                </div>
                                            </div>
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-certificate"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Tanda Selar</span>
                                                    <span class="detail-value" data-field="tanda_selar">-</span>
                                                </div>
                                            </div>
                                        </div>` : ''}
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Port of Registry</span>
                                                    <span class="detail-value" data-field="port_of_registry">-</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="detail-item">
                                                <div class="detail-icon">
                                                    <i class="fas fa-id-card"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Tanda Pendaftaran</span>
                                                    <span class="detail-value" data-field="tanda_pendaftaran">-</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Cards -->
                                <div class="menu-cards">
                                    <a href="/dokumen-kapal/view/${response.kapal.encoded_id}"
                                       class="menu-card text-decoration-none">
                                        <div class="menu-card-icon">
                                            <i class="fas fa-folder-open"></i>
                                        </div>
                                        <div class="menu-card-title">Dokumen</div>
                                    </a>

                                    <a href="{{ route('checklist-engine.index', ['id' => \App\Helpers\HashIdHelper::encode($tugboat->id)]) }}"
                                       class="menu-card text-decoration-none">
                                        <div class="menu-card-icon engine-icon">
                                            <i class="fas fa-cogs"></i>
                                        </div>
                                        <div class="menu-card-title">Checklist Engine</div>
                                    </a>
                                    
                                    <a href="/checklist-jangkar/${response.kapal.encoded_id}" 
                                       class="menu-card text-decoration-none">
                                        <div class="menu-card-icon anchor-icon">
                                            <i class="fas fa-anchor"></i>
                                        </div>
                                        <div class="menu-card-title">Checklist Perawatan Mesin Jangkar</div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Add to list
                    let rowContainer = modalBody.find('.row');
                    if (rowContainer.length === 0) {
                        modalBody.html(`<div class="row g-3">${newShipHtml}</div>`);
                    } else {
                        // Buat elemen baru dan tambahkan ke awal row
                        const newShipElement = $(newShipHtml);
                        rowContainer.children().first().before(newShipElement);
                    }
                    
                    // Initialize tooltips for new elements
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.map(function (tooltipTriggerEl) {
                        return new bootstrap.Tooltip(tooltipTriggerEl);
                    });
                    
                    // Reinitialize handlers for new elements
                    initializeHandlers();
                    
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            },
            error: function(xhr) {
                let errorMessage = 'Gagal menambahkan kapal';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            complete: function() {
                submitBtn.prop('disabled', false);
                submitBtn.html(submitBtn.data('original-text') || 'Tambah Kapal');
            }
        });
    });

    // Edit form submission
    $('#editKapalForm').on('submit', function(e) {
        e.preventDefault();
        
        // Check required fields
        let isValid = true;
        const jenisKapalId = $('#editJenisKapalId').val();
        const id = $('#editKapalId').val();
        
        // Validate common fields
        $(this).find('input[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        // Validate tugboat fields if applicable
        if (jenisKapalId == 1) {
            $('.tugboat-required').each(function() {
                if (!$(this).val()) {
                    isValid = false;
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });
        }
        
        if (!isValid) {
            Swal.fire({
                title: 'Error!',
                text: 'Mohon lengkapi semua field yang wajib diisi',
                icon: 'error',
                timer: 2000,
                showConfirmButton: false
            });
            return;
        }
        
        // Proceed with form submission
        let data = {
            _token: '{{ csrf_token() }}',
            _method: 'PUT',
            nama: $('#editKapalNama').val(),
            gt_nt: $('#editGtNt').val(),
            port_of_registry: $('#editPortOfRegistry').val(),
            tanda_pendaftaran: $('#editTandaPendaftaran').val(),
            rh_me_ps: $('#editRhMePs').val(),
            rh_me_sb: $('#editRhMeSb').val()
        };
        
        // Add tugboat specific fields if applicable
        if (jenisKapalId == 1) {
            data = {
                ...data,
                call_sign: $('#editCallSign').val(),
                imo: $('#editImo').val(),
                mesin: $('#editMesin').val(),
                tanda_selar: $('#editTandaSelar').val()
            };
        }
        
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        
        $.ajax({
            url: `/kapal/${id}`,
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    // Update nama di list
                    $(`#kapal-nama-${id}`).text(data.nama);
                    
                    // Update data attribute
                    $(`.edit-kapal[data-id="${id}"]`).data('nama', data.nama);
                    
                    // Update detail values secara realtime
                    const kapalCard = $(`#kapal-nama-${id}`).closest('.kapal-card');
                    
                    // Update common fields
                    kapalCard.find('.detail-value[data-field="gt_nt"]').text(data.gt_nt || '-');
                    kapalCard.find('.detail-value[data-field="port_of_registry"]').text(data.port_of_registry || '-');
                    kapalCard.find('.detail-value[data-field="tanda_pendaftaran"]').text(data.tanda_pendaftaran || '-');
                    
                    // Update tugboat specific fields
                    if (jenisKapalId == 1) {
                        kapalCard.find('.detail-value[data-field="call_sign"]').text(data.call_sign || '-');
                        kapalCard.find('.detail-value[data-field="imo"]').text(data.imo || '-');
                        kapalCard.find('.detail-value[data-field="mesin"]').text(data.mesin || '-');
                        kapalCard.find('.detail-value[data-field="tanda_selar"]').text(data.tanda_selar || '-');
                        kapalCard.find('.detail-value[data-field="rh_me_ps"]').text(data.rh_me_ps || '-');
                        kapalCard.find('.detail-value[data-field="rh_me_sb"]').text(data.rh_me_sb || '-');
                    }
                    
                    // Tutup modal
                    window.editModal.hide();
                    
                    // Tampilkan pesan sukses dan refresh halaman
                    Swal.fire({
                        title: 'Berhasil!',
                        text: response.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        window.location.reload(); // Refresh halaman setelah pesan sukses
                    });
                } else {
                    // Tampilkan pesan error dari server
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Gagal mengupdate data kapal',
                        icon: 'error',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            },
            error: function(xhr) {
                let errorMessage = 'Gagal mengupdate data kapal';
                if (xhr.status === 419) {
                    errorMessage = 'Sesi telah berakhir. Silakan muat ulang halaman.';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            complete: function() {
                submitBtn.prop('disabled', false);
                submitBtn.html('Simpan');
            }
        });
    });

    // Reset invalid state when input changes
    $('#editKapalForm input').on('input', function() {
        $(this).removeClass('is-invalid');
    });

    // Tambahkan event handler saat modal edit ditutup
    $('#editKapalModal').on('hidden.bs.modal', function () {
        // Reset form
        $('#editKapalForm')[0].reset();
        // Reset tombol submit ke text asli
        $('#editKapalForm button[type="submit"]').html('Simpan');
    });

    // Tambahkan event handler saat modal edit dibuka
    $('#editKapalModal').on('show.bs.modal', function () {
        // Pastikan tombol submit menampilkan text 'Simpan'
        $('#editKapalForm button[type="submit"]').html('Simpan');
    });

    // Detail button handlers
    $('[data-bs-target="#tugboatModal"]').on('click', function() {
        window.tugboatModal.show();
    });

    $('[data-bs-target="#tongkangModal"]').on('click', function() {
        window.tongkangModal.show();
    });

    // Utility functions
    function showAlert(type, message) {
        Swal.fire({
            title: type === 'success' ? 'Berhasil!' : 'Error!',
            text: message,
            icon: type,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true
        });
    }

    function refreshData() {
        $.get(window.location.href, function(response) {
            const doc = new DOMParser().parseFromString(response, 'text/html');
            
            // Update counts by finding the specific stat-value elements
            const tugboatCount = $(doc).find('.gradient-green .stat-value').text();
            const tongkangCount = $(doc).find('.gradient-blue .stat-value').text();
            
            // Update the count displays
            $('.gradient-green .stat-value').text(tugboatCount);
            $('.gradient-blue .stat-value').text(tongkangCount);
            
            // Update modal contents
            ['tugboat', 'tongkang'].forEach(type => {
                $(`#${type}Modal .modal-body`).html($(doc).find(`#${type}Modal .modal-body`).html());
            });

            // Reinitialize all handlers after content update
            initializeHandlers();
        });
    }

    // Initialize handlers on page load
    initializeHandlers();

    // Panggil handleDeleteDokumen saat dokumen dimuat
    handleDeleteDokumen();

    // Notification toggle
    $('#toggleNotifications').click(function() {
        const $button = $(this);
        const $body = $('#notificationBody');
        const $icon = $button.find('i');
        
        $body.slideToggle({
            duration: 300,
            start: function() {
                $button.prop('disabled', true);
            },
            complete: function() {
                $button.prop('disabled', false);
                
                // Update icon and class
                if ($body.is(':visible')) {
                    $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $button.removeClass('collapsed');
                } else {
                    $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $button.addClass('collapsed');
                }
                
                // Save state to localStorage
                localStorage.setItem('notificationsVisible', $body.is(':visible'));
            }
        });
    });
    
    // Restore notification visibility state
    const notificationsVisible = localStorage.getItem('notificationsVisible');
    if (notificationsVisible === 'false') {
        $('#notificationBody').hide();
        $('#toggleNotifications')
            .addClass('collapsed')
            .find('i')
            .removeClass('fa-chevron-up')
            .addClass('fa-chevron-down');
    }
});

// Tambahkan ini di awal file setelah DOMContentLoaded
function handleDeleteDokumen() {
    $('.delete-dokumen').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const btn = $(this);
        const dokumenId = btn.data('id');
        
        console.log('Delete button clicked for document:', dokumenId); // Debug line

        Swal.fire({
            title: 'Hapus Dokumen?',
            text: "Dokumen yang dihapus tidak dapat dikembalikan",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/dokumen-kapal/delete/' + dokumenId,
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        _method: 'DELETE'
                    },
                    success: function(response) {
                        btn.closest('.dokumen-item').fadeOut(300, function() {
                            $(this).remove();
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: 'Dokumen berhasil dihapus',
                                timer: 1500,
                                showConfirmButton: false
                            });
                        });
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Gagal menghapus dokumen',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    }
                });
            }
        });
    });
}

// Hapus event delegation yang ada di luar initializeHandlers
// $(document).off('click', '.delete-kapal').on('click', '.delete-kapal', function(e) { ... });

function handleDelete(id, jenisKapal, namaKapal) {
    Swal.fire({
        title: `Hapus ${jenisKapal}?`,
        text: `Apakah anda yakin ingin menghapus ${jenisKapal} "${namaKapal}"? Data yang dihapus tidak dapat dikembalikan`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading state
            Swal.fire({
                title: 'Menghapus...',
                text: 'Mohon tunggu sebentar',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: `/kapal/${id}`,
                method: 'DELETE',
                data: { _token: '{{ csrf_token() }}' },
                success: function(response) {
                    // Remove the card element
                    $(`#kapal-nama-${id}`).closest('.col-12').fadeOut(300, function() {
                        $(this).remove();
                        
                        // Update counts
                        $('.tugboat-card .stat-number:first').text(response.counts.tugboat);
                        $('.tongkang-card .stat-number:first').text(response.counts.tongkang);
                        
                        // Check if there are no more ships
                        const modalSelector = jenisKapal === 'Tugboat' ? '#tugboatModal' : '#tongkangModal';
                        const remainingShips = $(`${modalSelector} .kapal-card`).length;
                        
                        if (remainingShips === 0) {
                            // Show empty state if no ships remain
                            const emptyStateHtml = `
                                <div class="empty-state">
                                    <i class="fas ${jenisKapal === 'Tugboat' ? 'fa-ship' : 'fa-anchor'}"></i>
                                    <p>Belum ada ${jenisKapal.toLowerCase()} yang terdaftar</p>
                                </div>
                            `;
                            $(`${modalSelector} .modal-body`).html(emptyStateHtml);
                        }
                        
                        // Show success notification
                        Swal.fire({
                            title: 'Berhasil!',
                            text: `${jenisKapal} "${namaKapal}" berhasil dihapus`,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    });
                },
                error: function(xhr) {
                    let errorMessage = `Gagal menghapus ${jenisKapal}`;
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }
            });
        }
    });
}

// Add function to refresh modal content without page reload
function refreshModalContent() {
    $.get(`${window.location.pathname}/get-kapal-data`, function(response) {
        $('#tugboatModal .modal-body').html(response.tugboatContent);
        $('#tongkangModal .modal-body').html(response.tongkangContent);
        initializeHandlers();
    });
}

$(document).ready(function() {
    // Handle pagination click
    $('#notificationPagination').on('click', '.page-item:not(.disabled)', function() {
        const page = $(this).data('page');
        loadNotifications(page);
    });

    function loadNotifications(page) {
        // Show loading state
        $('#notificationList').addClass('loading');
        
        $.ajax({
            url: '{{ route("dashboard.notifications") }}',
            method: 'GET',
            data: { page: page },
            success: function(response) {
                // Update notification list
                $('#notificationList').html(response.html);
                
                // Update pagination
                updatePagination(response.currentPage, response.lastPage);
                
                // Update URL without refreshing
                const url = new URL(window.location);
                url.searchParams.set('page', page);
                window.history.pushState({}, '', url);
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Terjadi kesalahan saat memuat notifikasi',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            complete: function() {
                // Remove loading state
                $('#notificationList').removeClass('loading');
            }
        });
    }

    function updatePagination(currentPage, lastPage) {
        const pagination = $('#notificationPagination');
        pagination.empty();
        
        // Previous button
        pagination.append(`
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}" 
                data-page="${currentPage - 1}">
                <span class="page-link">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </li>
        `);
        
        // Page numbers
        for (let i = 1; i <= lastPage; i++) {
            pagination.append(`
                <li class="page-item ${i === currentPage ? 'active' : ''}" 
                    data-page="${i}">
                    <span class="page-link">${i}</span>
                </li>
            `);
        }
        
        // Next button
        pagination.append(`
            <li class="page-item ${currentPage === lastPage ? 'disabled' : ''}" 
                data-page="${currentPage + 1}">
                <span class="page-link">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </li>
        `);
    }
});

// Validasi input hanya angka untuk RH ME PS dan SB
$('#editRhMePs, #editRhMeSb').on('input', function() {
    // Hapus karakter selain angka
    this.value = this.value.replace(/[^0-9]/g, '');
    
    // Jika input kosong atau bukan angka, tandai sebagai invalid
    if (this.value === '' || isNaN(this.value)) {
        $(this).addClass('is-invalid');
    } else {
        $(this).removeClass('is-invalid');
    }
});

// Cegah paste teks yang bukan angka
$('#editRhMePs, #editRhMeSb').on('paste', function(e) {
    e.preventDefault();
    const text = (e.originalEvent.clipboardData || window.clipboardData).getData('text');
    if (!isNaN(text) && text.trim() !== '') {
        this.value = text.replace(/[^0-9]/g, '');
    }
});

$(document).ready(function() {
    // Handle form submission
    $('.form-kapal').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalBtnText = submitBtn.html();
        
        // Disable submit button and show loading state
        submitBtn.prop('disabled', true)
                .html('<i class="fas fa-spinner fa-spin me-2"></i>Menyimpan...');
        
        $.ajax({
            url: '{{ route("kapal.store") }}',
            method: 'POST',
            data: form.serialize(),
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Kapal berhasil ditambahkan',
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        // Reload page
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: xhr.responseJSON?.message || 'Terjadi kesalahan saat menyimpan data',
                    timer: 1500,
                    showConfirmButton: false
                });
            },
            complete: function() {
                // Reset submit button
                submitBtn.prop('disabled', false)
                        .html(originalBtnText);
            }
        });
    });
});

// ... existing code ...
function updateKapal(id, data) {
    $.ajax({
        url: `/kapal/${id}`,
        method: 'PUT',
        data: data,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: 'Data kapal berhasil diupdate',
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    // Refresh halaman
                    window.location.reload();
                });
            }
        },
        error: function(xhr) {
            // Show error message
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat mengupdate data',
                timer: 1500,
                showConfirmButton: false
            });
        }
    });
}
// ... existing code ...

    $(document).ready(function() {
        // Toggle untuk notifikasi umum
        $('#toggleNotifications').click(function() {
            $(this).find('i').toggleClass('fa-chevron-up fa-chevron-down');
            $('#notificationBody').slideToggle();
        });

        // Toggle untuk notifikasi engine
        $('#toggleEngineNotifications').click(function() {
            $(this).find('i').toggleClass('fa-chevron-up fa-chevron-down');
            $('#engineNotificationBody').slideToggle();
        });

        // Inisialisasi semua accordion
        var accordions = document.querySelectorAll('.accordion-collapse');
        accordions.forEach(function(accordion) {
            accordion.addEventListener('show.bs.collapse', function () {
                // Tutup accordion lain saat satu dibuka
                accordions.forEach(function(otherAccordion) {
                    if (otherAccordion !== accordion && otherAccordion.classList.contains('show')) {
                        bootstrap.Collapse.getInstance(otherAccordion).hide();
                    }
                });
            });
        });

        // Tambahkan event listener untuk button accordion
        $('.accordion-button').on('click', function(e) {
            e.preventDefault();
            var target = $(this).data('bs-target');
            $(target).collapse('toggle');
        });
    });
</script>
@endpush

