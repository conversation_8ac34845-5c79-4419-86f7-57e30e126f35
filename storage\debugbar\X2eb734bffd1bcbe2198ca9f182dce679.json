{"__meta": {"id": "X2eb734bffd1bcbe2198ca9f182dce679", "datetime": "2025-07-20 08:26:57", "utime": **********.887628, "method": "GET", "uri": "/admin/kantor/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[08:26:57] LOG.info: Stok Kapal Kosong: {\n    \"query\": [],\n    \"result\": [\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 225,\n            \"nama_barang\": \"FUEL FILTER SCREEN\",\n            \"nomor_seri\": \"FUEL FILTER SCREEN\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 226,\n            \"nama_barang\": \"PRESSURE TRANSMITER\",\n            \"nomor_seri\": \"04541 - 90200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 227,\n            \"nama_barang\": \"GASKET CYL HEAD (TSP)\",\n            \"nomor_seri\": \"3750112200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 228,\n            \"nama_barang\": \"PACKING, ROCKER CASE\",\n            \"nomor_seri\": \"3750441200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 229,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"3750402300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 230,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550710200\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 231,\n            \"nama_barang\": \"SEAL, VALVE STEM\",\n            \"nomor_seri\": \"3750400900\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 232,\n            \"nama_barang\": \"O-RING\",\n            \"nomor_seri\": \"0550531065\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 233,\n            \"nama_barang\": \"ORING LINER\",\n            \"nomor_seri\": \"3750732400\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 234,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704201\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        },\n        {\n            \"kapal_id\": 4,\n            \"nama_kapal\": \"TB. GEMA 201\",\n            \"jenis_kapal_id\": 1,\n            \"id_barang\": 235,\n            \"nama_barang\": \"ORING\",\n            \"nomor_seri\": \"3710704300\",\n            \"jenis\": \"MITSUBISHI S6R2-MTK3L\",\n            \"satuan\": \"PCS\",\n            \"stok_tersedia\": \"0\",\n            \"jumlah_pengeluaran\": 0\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.633109, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.307911, "end": **********.887652, "duration": 0.5797410011291504, "duration_str": "580ms", "measures": [{"label": "Booting", "start": **********.307911, "relative_start": 0, "end": **********.524274, "relative_end": **********.524274, "duration": 0.21636319160461426, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.524291, "relative_start": 0.21638011932373047, "end": **********.887655, "relative_end": 3.0994415283203125e-06, "duration": 0.36336398124694824, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25901424, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "admin_kantor.dashboard", "param_count": null, "params": [], "start": **********.648562, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/admin_kantor/dashboard.blade.phpadmin_kantor.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fadmin_kantor%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.880252, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.881495, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.883494, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/kantor/dashboard", "middleware": "web, auth, admin.kantor", "controller": "App\\Http\\Controllers\\AdminKantorDashboardController@index", "namespace": null, "prefix": "", "where": [], "as": "admin.kantor.dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=13\" onclick=\"\">app/Http/Controllers/AdminKantorDashboardController.php:13-169</a>"}, "queries": {"nb_statements": 15, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03103, "accumulated_duration_str": "31.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.567217, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.571713, "duration": 0.019870000000000002, "duration_str": "19.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 64.035}, {"sql": "select count(*) as aggregate from `pembelian_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.599622, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:16", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=16", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "16"}, "connection": "gema_kapal", "explain": null, "start_percent": 64.035, "width_percent": 6.897}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.607209, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:19", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=19", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "19"}, "connection": "gema_kapal", "explain": null, "start_percent": 70.931, "width_percent": 7.38}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.610603, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:23", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=23", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "23"}, "connection": "gema_kapal", "explain": null, "start_percent": 78.311, "width_percent": 1.482}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-20'", "type": "query", "params": [], "bindings": ["2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.61215, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:24", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=24", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "24"}, "connection": "gema_kapal", "explain": null, "start_percent": 79.794, "width_percent": 1.321}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6135979, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:30", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=30", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "30"}, "connection": "gema_kapal", "explain": null, "start_percent": 81.115, "width_percent": 0.612}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.614774, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:36", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=36", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "36"}, "connection": "gema_kapal", "explain": null, "start_percent": 81.727, "width_percent": 0.677}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.615918, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:42", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=42", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "42"}, "connection": "gema_kapal", "explain": null, "start_percent": 82.404, "width_percent": 0.516}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where date(`created_at`) = '2025-07-19'", "type": "query", "params": [], "bindings": ["2025-07-19"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6170971, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:43", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=43", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "43"}, "connection": "gema_kapal", "explain": null, "start_percent": 82.92, "width_percent": 0.935}, {"sql": "select count(*) as aggregate from `data_sparepart`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 49}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.61866, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:49", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=49", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "49"}, "connection": "gema_kapal", "explain": null, "start_percent": 83.854, "width_percent": 4.641}, {"sql": "select count(*) as aggregate from `data_sparepart` where month(`created_at`) = '06'", "type": "query", "params": [], "bindings": ["06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.621193, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:54", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=54", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "54"}, "connection": "gema_kapal", "explain": null, "start_percent": 88.495, "width_percent": 1.031}, {"sql": "select count(*) as aggregate from `pembelian_kantor` where `created_at` >= '2025-07-06 08:26:57'", "type": "query", "params": [], "bindings": ["2025-07-06 08:26:57"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.622539, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=61", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "61"}, "connection": "gema_kapal", "explain": null, "start_percent": 89.526, "width_percent": 0.612}, {"sql": "select count(*) as aggregate from `pengeluaran_kantor` where `created_at` >= '2025-07-06 08:26:57'", "type": "query", "params": [], "bindings": ["2025-07-06 08:26:57"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6237068, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:64", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=64", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "64"}, "connection": "gema_kapal", "explain": null, "start_percent": 90.139, "width_percent": 0.773}, {"sql": "select `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, (\nSELECT COALESCE(SUM(CASE\nWHEN jenis_transaksi = \"penerimaan_sparepart_kapal\" THEN jumlah\nWHEN jenis_transaksi = \"pemakaian_sparepart_kapal\" THEN -jumlah\nELSE 0\nEND), 0)\nFROM riwayat_stok_kantor\nWHERE id_barang = ds.id\nAND jenis_transaksi IN (\"penerimaan_sparepart_kapal\", \"pemakaian_sparepart_kapal\")\n) as stok_tersedia, (\nSELECT GROUP_CONCAT(DISTINCT pk.kapal_id)\nFROM pengeluaran_kantor pk\nJOIN pembelian_kantor pmk ON pmk.id = pk.id_pembelian\nWHERE pmk.id_barang = ds.id\nAND pk.jumlah > 0\n) as kapal_dengan_stok from `data_sparepart` as `ds` where exists (select 1 from `riwayat_stok_kantor` where riwayat_stok_kantor.id_barang = ds.id and `jenis_transaksi` in ('penerimaan_sparepart_kapal', 'pemakaian_sparepart_kapal')) having `stok_tersedia` <= 0 and `kapal_dengan_stok` = null", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>_sparepart_kapal", "pema<PERSON><PERSON>_sparepart_kapal", 0, null], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 100}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6254199, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:100", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=100", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "100"}, "connection": "gema_kapal", "explain": null, "start_percent": 90.912, "width_percent": 5.801}, {"sql": "select `k`.`id` as `kapal_id`, `k`.`nama` as `nama_kapal`, `k`.`jenis_kapal_id`, `ds`.`id` as `id_barang`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan`, COALESCE(SUM(prk.jumlah), 0) as stok_tersedia, MAX(pk.jumlah) as jumlah_pengeluaran from `kapal` as `k` inner join `pengeluaran_kantor` as `pk` on `k`.`id` = `pk`.`kapal_id` inner join `pembelian_kantor` as `pmk` on `pmk`.`id` = `pk`.`id_pembelian` inner join `data_sparepart` as `ds` on `pmk`.`id_barang` = `ds`.`id` left join `penerimaan_kapal` as `prk` on `k`.`id` = `prk`.`kapal_id` and `pk`.`id` = `prk`.`pengeluaran_kantor_id` where exists (select 1 from `pengeluaran_kantor` inner join `pembelian_kantor` on `pembelian_kantor`.`id` = `pengeluaran_kantor`.`id_pembelian` where `pengeluaran_kantor`.`kapal_id` = `k`.`id` and `pembelian_kantor`.`id_barang` = `ds`.`id`) group by `k`.`id`, `k`.`nama`, `k`.`jenis_kapal_id`, `ds`.`id`, `ds`.`nama_barang`, `ds`.`nomor_seri`, `ds`.`jenis`, `ds`.`satuan` having `stok_tersedia` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.628657, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "AdminKantorDashboardController.php:143", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/AdminKantorDashboardController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\AdminKantorDashboardController.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FAdminKantorDashboardController.php&line=143", "ajax": false, "filename": "AdminKantorDashboardController.php", "line": "143"}, "connection": "gema_kapal", "explain": null, "start_percent": 96.713, "width_percent": 3.287}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/admin/kantor/dashboard\"\n]"}, "request": {"path_info": "/admin/kantor/dashboard", "status_code": "<pre class=sf-dump id=sf-dump-1230777232 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1230777232\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-380322763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-380322763\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-939103539 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-939103539\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-244672870 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://sikapal.test/voyage-report</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjhYUGt4YldjQlNsQ1pQSzFnU1ZHckE9PSIsInZhbHVlIjoidmlRNzlYZHlneVBqWVBNVXJpQXF1ekFleng0VnZQcmpTbDMyOVdGSTVsWG5tU3VUZzJPN3JOcXRzRkZsTmpQMDVyODVGLzE2NjEwVzR5aXl6ZkRHV1NvTDk0WUFSaC9MUXo4YTdORk4vbkQvUW1KZVl5WUlQcDZjU3JkMGxaMHIiLCJtYWMiOiI5YTg0NjQ2MGZlOGNmZGM2MzM3OWNjOTQzNjU5MzBmNDQ4OWE1YTRmYTk5ZjRmNjJiYmE5YmYxYTkwN2E2ZjZjIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IkRvSlVYTDcxZzJJSVFyTnA4NnBKL2c9PSIsInZhbHVlIjoicnhteitaT0o5YWRoSjhBV0ZuUXcvU09SN0RzU21yZU0veTJPR3NTYVRDeHRpSVoyU1dGanhhTmVTeUVpTjVLaHFZbDcvOWp2QU8reHRZb25DTnZUTFZNYy9GZGZYbU9YRUdtT1RpWm1TWnFKZktsRWJrV25iVVYwdFNvaGhmSCsiLCJtYWMiOiJlZjQ3ZTFjN2M5Yjc0YTFkOGEzMzNjZDJiMzcwNGFlODhmZjVjYjFiYWNjMWU3MGFlMzdkYTRhNDM2ZWM1MzIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244672870\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1868267177 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UzQ0pc6RmrKE4RDThQWHQCcm6W8XubcZ3PmVp0O1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868267177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-222034597 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:26:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJTTkk2cjltd2VOOEFtWlp0TUJ1TWc9PSIsInZhbHVlIjoiYjVXRDFCY0dJcTJxU1Bsd3ZobjhCanNDYWY5NHE4N2RnQ2xGV2Q4NkdQSnFtSlA3THFaZXU2TXF5U2kxVjRtNklNaDkreUhKZDBjYk1yanJ6NURMSjkweHlhMGhLbWRIallHOXd3TkJTY2FpMysxMlZXM1BxbWRxUnAvb2FmRDMiLCJtYWMiOiI2YWQ0YzdkMzEyMjgzNmFhYjM5M2FiOThhZjIyZTIyYjk3ZjNiZDMzNTUwYzFlZTVjNTBhMTc2OWUwOWY5OTY3IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:26:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6IlgyejVsbmNwY0V1blFDT0dwTEh3bmc9PSIsInZhbHVlIjoib21pazlJTTR6M0UrVUtwdXhlZFg5VTdhdlFhY0EvcEZCV1ZPQXRBQVRrRXpQL0hHRWl2Q29QaFRCS3VXR3hhVFhDK3hBd3p3RzlySkFWYm1ORTFmUVErUzhGVHg5NmVLZXB4T2NZMVhuWG9QMWp5UkJ2RjFyRVYxUjRpeSsrYVciLCJtYWMiOiIxYTU1ZjQ4MTRjYzc5ZDA0ZDk1YTUyOGQxYjRkYmQxMWJmYWQzZTU5MWJhZWJkNjAxZTEzMjQ5MzlmYmVjYTY0IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:26:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJTTkk2cjltd2VOOEFtWlp0TUJ1TWc9PSIsInZhbHVlIjoiYjVXRDFCY0dJcTJxU1Bsd3ZobjhCanNDYWY5NHE4N2RnQ2xGV2Q4NkdQSnFtSlA3THFaZXU2TXF5U2kxVjRtNklNaDkreUhKZDBjYk1yanJ6NURMSjkweHlhMGhLbWRIallHOXd3TkJTY2FpMysxMlZXM1BxbWRxUnAvb2FmRDMiLCJtYWMiOiI2YWQ0YzdkMzEyMjgzNmFhYjM5M2FiOThhZjIyZTIyYjk3ZjNiZDMzNTUwYzFlZTVjNTBhMTc2OWUwOWY5OTY3IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:26:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6IlgyejVsbmNwY0V1blFDT0dwTEh3bmc9PSIsInZhbHVlIjoib21pazlJTTR6M0UrVUtwdXhlZFg5VTdhdlFhY0EvcEZCV1ZPQXRBQVRrRXpQL0hHRWl2Q29QaFRCS3VXR3hhVFhDK3hBd3p3RzlySkFWYm1ORTFmUVErUzhGVHg5NmVLZXB4T2NZMVhuWG9QMWp5UkJ2RjFyRVYxUjRpeSsrYVciLCJtYWMiOiIxYTU1ZjQ4MTRjYzc5ZDA0ZDk1YTUyOGQxYjRkYmQxMWJmYWQzZTU5MWJhZWJkNjAxZTEzMjQ5MzlmYmVjYTY0IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:26:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222034597\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146457224 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dpZDqS3TeewksSYqjrS7hOZGdlsIyDauIaAkLu4X</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://sikapal.test/admin/kantor/dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146457224\", {\"maxDepth\":0})</script>\n"}}