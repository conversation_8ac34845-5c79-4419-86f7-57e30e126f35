<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\PasswordController;
use App\Http\Controllers\KapalController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DokumenKapalController;
use App\Http\Controllers\ChecklistJangkarController;
use App\Http\Controllers\ChecklistEngineController;
use App\Http\Controllers\AdminKantorDashboardController;
use App\Http\Controllers\PembelianKantorController;
use App\Http\Controllers\PengeluaranKantorController;
use App\Http\Controllers\DataSparepartController;
use App\Http\Controllers\PenerimaanKapalController;
use App\Http\Controllers\PemakaianKapalController;
use App\Http\Controllers\StokSparepartController;
use App\Http\Controllers\JadwalDockingController;
use App\Http\Controllers\JadwalDockingTongkangController;
use App\Http\Controllers\AturJadwalController;
use App\Http\Controllers\CrewController;
use App\Http\Controllers\VoyageReportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect('/login'); 
})->middleware('guest');

// Guest routes
Route::middleware('guest')->group(function() {
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
});

// Auth routes
Route::middleware('auth')->group(function() {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    
    // Jadwal Docking Routes
    Route::get('/jadwal-docking', [JadwalDockingController::class, 'index'])->name('jadwal-docking.index');
    Route::post('/jadwal-docking', [JadwalDockingController::class, 'store'])->name('jadwal-docking.store');
    Route::get('/jadwal-docking/{id}', [JadwalDockingController::class, 'show'])->name('jadwal-docking.show');
    Route::put('/jadwal-docking/{id}', [JadwalDockingController::class, 'update'])->name('jadwal-docking.update');
    Route::delete('/jadwal-docking/{id}', [JadwalDockingController::class, 'destroy'])->name('jadwal-docking.destroy');
    Route::put('/jadwal-docking/{id}/tunda', [JadwalDockingController::class, 'tunda'])->name('jadwal-docking.tunda');
    Route::put('/jadwal-docking/{id}/selesai', [JadwalDockingController::class, 'selesai'])->name('jadwal-docking.selesai');
    
    // Jadwal Docking Tongkang Routes
    Route::get('/jadwal-docking-tongkang', [JadwalDockingTongkangController::class, 'index'])->name('jadwal-docking-tongkang.index');
    Route::post('/jadwal-docking-tongkang', [JadwalDockingTongkangController::class, 'store'])->name('jadwal-docking-tongkang.store');
    Route::get('/jadwal-docking-tongkang/{id}', [JadwalDockingTongkangController::class, 'show'])->name('jadwal-docking-tongkang.show');
    Route::put('/jadwal-docking-tongkang/{id}', [JadwalDockingTongkangController::class, 'update'])->name('jadwal-docking-tongkang.update');
    Route::delete('/jadwal-docking-tongkang/{id}', [JadwalDockingTongkangController::class, 'destroy'])->name('jadwal-docking-tongkang.destroy');
    Route::put('/jadwal-docking-tongkang/{id}/tunda', [JadwalDockingTongkangController::class, 'tunda'])->name('jadwal-docking-tongkang.tunda');
    Route::put('/jadwal-docking-tongkang/{id}/selesai', [JadwalDockingTongkangController::class, 'selesai'])->name('jadwal-docking-tongkang.selesai');
    
    // Atur Jadwal Universal Routes
    Route::get('/atur-jadwal', [AturJadwalController::class, 'index'])->name('atur-jadwal.index');
    Route::get('/atur-jadwal/get-kapal-by-jenis', [AturJadwalController::class, 'getKapalByJenis'])->name('atur-jadwal.get-kapal-by-jenis');
    Route::post('/atur-jadwal', [AturJadwalController::class, 'store'])->name('atur-jadwal.store');
    Route::get('/atur-jadwal/{id}', [AturJadwalController::class, 'show'])->name('atur-jadwal.show');
    Route::put('/atur-jadwal/{id}', [AturJadwalController::class, 'update'])->name('atur-jadwal.update');
    Route::delete('/atur-jadwal/{id}', [AturJadwalController::class, 'destroy'])->name('atur-jadwal.destroy');
    Route::put('/atur-jadwal/{id}/tunda', [AturJadwalController::class, 'tunda'])->name('atur-jadwal.tunda');
    Route::put('/atur-jadwal/{id}/selesai', [AturJadwalController::class, 'selesai'])->name('atur-jadwal.selesai');
    
    Route::get('/change-password', [PasswordController::class, 'showChangePasswordForm'])->name('change.password');
    Route::post('/change-password', [PasswordController::class, 'changePassword'])->name('update.password');
    
    Route::post('/kapal', [KapalController::class, 'store'])->name('kapal.store');
    Route::put('/kapal/{id}', [KapalController::class, 'update'])->name('kapal.update');
    Route::post('/kapal/{id}', [KapalController::class, 'update']);
    Route::delete('/kapal/{kapal}', [KapalController::class, 'destroy'])->name('kapal.destroy');
    

    
    Route::get('/dashboard/get-kapal-data', [DashboardController::class, 'getKapalData'])->name('dashboard.getKapalData');
    
    Route::get('/kapal/{kapal}', [KapalController::class, 'show'])->name('kapal.show');
    

    
    Route::get('/dashboard/notifications', [DashboardController::class, 'getNotificationsPage'])->name('dashboard.notifications');
    
    Route::prefix('checklist-jangkar')->group(function() {
        Route::get('/{kapal}', [ChecklistJangkarController::class, 'index'])->name('checklist-jangkar.index');
        Route::post('/{kapal}', [ChecklistJangkarController::class, 'store'])->name('checklist-jangkar.store');
        Route::delete('/{kapal}/{checklist}', [ChecklistJangkarController::class, 'destroy'])->name('checklist-jangkar.destroy');
        Route::get('/detail/{checklist}', [ChecklistJangkarController::class, 'detail'])->name('checklist-jangkar.detail');
        Route::get('/download/{checklist}', [ChecklistJangkarController::class, 'download'])->name('checklist-jangkar.download');
    });
    
    Route::prefix('checklist-engine')->group(function() {
        Route::get('/{id}', [ChecklistEngineController::class, 'index'])->name('checklist-engine.index');
        Route::post('/store/{kapal}', [ChecklistEngineController::class, 'store'])->name('checklist-engine.store');
        Route::post('/store-result', [ChecklistEngineController::class, 'storeResult'])->name('checklist-engine.store-result');
        Route::delete('/delete/{kapal}/{checklist}', [ChecklistEngineController::class, 'destroy'])->name('checklist-engine.destroy');
        Route::get('/detail/{checklist}', [ChecklistEngineController::class, 'detail'])->name('checklist-engine.detail');
        Route::get('/download/{checklist}', [ChecklistEngineController::class, 'download'])->name('checklist-engine.download');
        Route::post('/update-perjalanan/{kapal}', [ChecklistEngineController::class, 'updatePerjalanan'])->name('checklist-engine.update-perjalanan');
        Route::delete('/delete-histori/{kapal}/{histori}', [ChecklistEngineController::class, 'deleteHistori'])->name('checklist-engine.delete-histori');
    });
});

Route::middleware(['auth'])->group(function () {
    Route::get('/checklist-engine/{kapal}', function (Kapal $kapal) {
        if ($kapal->jenis_kapal->nama === 'Tongkang') {
            abort(404);
        }
        // ... kode yang ada ...
    })->name('checklist-engine.show');
    
    Route::get('/checklist-engine/pdf/{kapal}', function (Kapal $kapal) {
        if ($kapal->jenis_kapal->nama === 'Tongkang') {
            abort(404);
        }
        // ... kode yang ada ...
    })->name('checklist-engine.pdf');
    // ... route lainnya ...

    // Route untuk admin kapal
    Route::middleware(['admin.kapal'])->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        // ... route lainnya untuk admin kapal
    });

    // Route untuk admin kantor
    Route::middleware(['admin.kantor'])->group(function () {
        Route::get('/admin/kantor/dashboard', [AdminKantorDashboardController::class, 'index'])->name('admin.kantor.dashboard');
        // ... route lainnya untuk admin kantor
    });

    // Penerimaan Kapal Routes
    Route::get('/penerimaan-kapal/{kapal}', [PenerimaanKapalController::class, 'index'])
        ->name('penerimaan-kapal.index');
    Route::post('/penerimaan-kapal/terima', [PenerimaanKapalController::class, 'terima'])
        ->name('penerimaan-kapal.terima');
    
    // Pemakaian Kapal Routes  
    Route::get('/pemakaian-kapal/{kapal}', [PemakaianKapalController::class, 'index'])
        ->name('pemakaian-kapal.index');
    Route::post('/pemakaian-kapal/pakai', [PemakaianKapalController::class, 'store'])
        ->name('pemakaian-kapal.store');

    Route::get('/stok-sparepart', [StokSparepartController::class, 'index'])->name('stok-sparepart.index');

    // Stok Sparepart Export Routes
    Route::get('/stok-sparepart/export-form', [StokSparepartController::class, 'exportForm'])
        ->name('stok-sparepart.export-form');
    Route::get('stok-sparepart/export', [StokSparepartController::class, 'export'])->name('stok-sparepart.export');

    Route::post('/checklist-engine/store-item', [ChecklistEngineController::class, 'storeItemPemeriksaan'])->name('checklist-engine.store-item');

    Route::post('/stok-sparepart/import', [StokSparepartController::class, 'import'])->name('stok-sparepart.import');
    Route::get('/stok-sparepart/import', [StokSparepartController::class, 'importForm'])->name('stok-sparepart.import-form');
});

Route::get('/checklist-engine/download/{id}', [ChecklistEngineController::class, 'download'])
    ->name('checklist-engine.download');

// Dokumen Kapal Routes - Admin Kapal dan Admin Kantor dengan pembatasan akses
Route::middleware(['auth'])->prefix('dokumen-kapal')->group(function() {
    Route::post('/', [DokumenKapalController::class, 'store'])->name('dokumen.store');
    Route::delete('/delete/{id}', [DokumenKapalController::class, 'destroy'])->name('dokumen.destroy');
    Route::get('/view/{kapal}', [DokumenKapalController::class, 'show'])->name('dokumen.show');
    Route::get('/{id}', [DokumenKapalController::class, 'detail'])->name('dokumen.detail');
    Route::match(['post', 'put'], '/update/{id}', [DokumenKapalController::class, 'update'])->name('dokumen.update');
    Route::post('/toggle-checklist/{id}', [DokumenKapalController::class, 'toggleChecklist'])->name('dokumen.toggle-checklist');
    Route::get('/download/{kapal}', [DokumenKapalController::class, 'downloadList'])->name('dokumen.download');
});

Route::middleware(['auth', 'admin.kantor'])->group(function () {
    // Daftar Kapal untuk Admin Kantor
    Route::get('/admin/kantor/kapal', [AdminKantorDashboardController::class, 'daftarKapal'])->name('admin.kantor.kapal');

    // Pembelian Kantor Routes
    Route::get('pembelian-kantor', [PembelianKantorController::class, 'index'])->name('pembelian-kantor.index');
    Route::post('pembelian-kantor', [PembelianKantorController::class, 'store'])->name('pembelian-kantor.store');
    Route::put('pembelian-kantor/{id}', [PembelianKantorController::class, 'update'])->name('pembelian-kantor.update');
    Route::delete('pembelian-kantor/{id}', [PembelianKantorController::class, 'destroy'])->name('pembelian-kantor.destroy');
    Route::post('pembelian-kantor/export', [PembelianKantorController::class, 'export'])
        ->name('pembelian-kantor.export');

    // Riwayat Pembelian Routes
    Route::get('riwayat-pembelian', [PembelianKantorController::class, 'riwayat'])->name('riwayat-pembelian.index');
    Route::post('riwayat-pembelian/export', [PembelianKantorController::class, 'exportRiwayat'])
        ->name('riwayat-pembelian.export');
      
    Route::resource('pengeluaran-kantor', PengeluaranKantorController::class);
    Route::resource('sparepart', DataSparepartController::class);
    
    // Crew Routes
    Route::resource('crew', CrewController::class);
    
    // Voyage Report Routes
    Route::get('voyage-report/accumulated-stats', [VoyageReportController::class, 'getAccumulatedStats'])->name('voyage-report.accumulated-stats');
    Route::resource('voyage-report', VoyageReportController::class);
    Route::put('voyage-report/{voyageReport}/approve', [VoyageReportController::class, 'approve'])->name('voyage-report.approve');
    Route::put('voyage-report/{voyageReport}/reject', [VoyageReportController::class, 'reject'])->name('voyage-report.reject');
    Route::get('voyage-report/{voyageReport}/download-lampiran', [VoyageReportController::class, 'downloadLampiran'])->name('voyage-report.download-lampiran');
});

Route::post('/penerimaan-kapal/terima', [PenerimaanKapalController::class, 'terima'])->name('penerimaan-kapal.terima');

Route::post('/pemakaian-kapal', [PemakaianKapalController::class, 'store'])->name('pemakaian-kapal.store');

Route::get('/penerimaan-kapal/{kapal}', [PenerimaanKapalController::class, 'index'])
    ->name('penerimaan-kapal.index');

Route::post('pengeluaran-kantor/export', [PengeluaranKantorController::class, 'export'])
    ->name('pengeluaran-kantor.export');

Route::post('/penerimaan-kapal/export', [PenerimaanKapalController::class, 'export'])->name('penerimaan-kapal.export');

Route::post('/pemakaian-kapal/export', [PemakaianKapalController::class, 'export'])->name('pemakaian-kapal.export');

Route::get('/checklist-engine/{id}', [ChecklistEngineController::class, 'index'])->name('checklist-engine.index');
Route::get('/checklist-engine/{id}/export', [ChecklistEngineController::class, 'export'])->name('checklist-engine.export');

Route::post('/penerimaan-kapal/{id}/batal', [PenerimaanKapalController::class, 'batalkan'])
    ->name('penerimaan-kapal.batal');

Route::post('/pemakaian-kapal/{id}/batal', [PemakaianKapalController::class, 'batalkan'])
    ->name('pemakaian-kapal.batal');
