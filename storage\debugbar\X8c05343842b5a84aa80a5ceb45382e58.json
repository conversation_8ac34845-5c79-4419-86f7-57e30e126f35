{"__meta": {"id": "X8c05343842b5a84aa80a5ceb45382e58", "datetime": "2025-07-20 08:42:26", "utime": **********.628468, "method": "POST", "uri": "/dokumen-kapal/toggle-checklist/41", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.330481, "end": **********.628495, "duration": 0.29801392555236816, "duration_str": "298ms", "measures": [{"label": "Booting", "start": **********.330481, "relative_start": 0, "end": **********.528297, "relative_end": **********.528297, "duration": 0.1978158950805664, "duration_str": "198ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.528318, "relative_start": 0.19783687591552734, "end": **********.628497, "relative_end": 1.9073486328125e-06, "duration": 0.10017895698547363, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 25919024, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST dokumen-kapal/toggle-checklist/{id}", "middleware": "web, auth, Closure", "controller": "App\\Http\\Controllers\\DokumenKapalController@toggleChecklist", "namespace": null, "prefix": "/dokumen-kapal", "where": [], "as": "dokumen.toggle-checklist", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=251\" onclick=\"\">app/Http/Controllers/DokumenKapalController.php:251-277</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02226, "accumulated_duration_str": "22.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.571376, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.575627, "duration": 0.013689999999999999, "duration_str": "13.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 61.5}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`id` = '41' limit 1", "type": "query", "params": [], "bindings": ["41"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 253}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5971951, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:253", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 253}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=253", "ajax": false, "filename": "DokumenKapalController.php", "line": "253"}, "connection": "gema_kapal", "explain": null, "start_percent": 61.5, "width_percent": 2.201}, {"sql": "update `dokumen_kapal` set `check_list_chief` = 1, `dokumen_kapal`.`updated_at` = '2025-07-20 08:42:26' where `id` = 41", "type": "query", "params": [], "bindings": [1, "2025-07-20 08:42:26", 41], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 267}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.60994, "duration": 0.00808, "duration_str": "8.08ms", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:267", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 267}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=267", "ajax": false, "filename": "DokumenKapalController.php", "line": "267"}, "connection": "gema_kapal", "explain": null, "start_percent": 63.702, "width_percent": 36.298}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\DokumenKapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd\"\n]"}, "request": {"path_info": "/dokumen-kapal/toggle-checklist/41", "status_code": "<pre class=sf-dump id=sf-dump-595912824 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-595912824\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-502115254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-502115254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-743980478 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">chief</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743980478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1742933157 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpxSnh5Ky92T25ManJ5Z1kvVjVqd1E9PSIsInZhbHVlIjoiM2xMWElYYlBWNjNTRE1RVWlLdk1kT3hzLzcybVhEMnVDWUsrbmpNaGN6OFJGL1JZWEwzS0RhdDJ5Uks1N0dOQUR0UkJzZm94M3E4Ris4TzFOLzYvM0U5MVN2dk5vZkQrcHRxMWpLa0N1d0IrV2s2Y3NlWkNoQU83aTBRV2Z1Z2wiLCJtYWMiOiIzNDAzZGRkNDM2OGUyMjM4MmNlMDFiODIyMjY1OWU1NWE0NWE2M2YyZmI5NjBlMzYxZDRlYWQ4MjFiMmY4YjI2IiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IndkT2p1bHNORFNWNHFVQVh0bm5xUHc9PSIsInZhbHVlIjoicnBaUW1rN0UzRkh6a2RXN3NLcXMrWjgvWWhobER6YTVWSHE2R0M1V3hyWC9PVjNiSEViTU9Lcm9tNW9aNWphWndCdndVZmtFV2tLZFBoM1N4TlZVSDlvclY3SnZnQnBBOTA2NWdIeTc4eGw2TFdJckJlUms2Ri9JMXl6VUkwNlUiLCJtYWMiOiJhZGVjY2MyYjFkN2YzYzhhNzQyYTFjNzlhM2JiYTgwOTI4ZjBiZGEzMmJiNDM0OTMwMDRhNzI5NDQzMGVjYzNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742933157\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1234255025 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z47ckW1sW3vByhJlsabzz4Jt9SLwE6fzIeTpobGt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234255025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-571779260 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:42:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkNzSFlKZXhjT1ovOXdVdEUxMlhJbXc9PSIsInZhbHVlIjoieFlzNUxFTUJZd09CaytKZ25ZNWNwMHM4ejNBNTBWR1dYamFjZVNrcU1jZGJVSXJQR21DSVRHUVlNMDRZckFYclJBU3hWZ0hNVXVMWThNVFM3c0w0REhyWUdGMVFDYldOWmxzR21WR0wvTU8yOVc0cHNGbms5Q0lWSXVsOVlnK1kiLCJtYWMiOiJkMzE4MjIxYmFlODEyODZmOTY2OWJjOWQ0M2M1NDQwNGQ4ZmI2MjNlM2RlYmQwMmZiZWUzNzYxZGYxNzQ4M2JlIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImE3OW9PNjdMc0hxTjdMMjcyN3hoWFE9PSIsInZhbHVlIjoiU2VIRGhrVENCeWRYZkpXOXZqbFEvWGFZcmdBdUYxalNpYWtuRTM3c3lJc1l6V25xVVFna1VpN21lRW14dEM2TE9OQ2ErdmFLekw0MjVWaG1VTnJDSERKdjQ5Q21GZ3FsTUV4RWtQZVQ2VUcyVHpyc3VTeTJ0NW9vY0dzckE0NEMiLCJtYWMiOiIwYTA1NTI1MmZjOTc5YmY2ZmU2Y2VlODlhN2I1MTY4YzI4MzNlZjY4ZWMzZTFjNzhmZTg0OGNmYTk4NjkxMTY1IiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:42:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkNzSFlKZXhjT1ovOXdVdEUxMlhJbXc9PSIsInZhbHVlIjoieFlzNUxFTUJZd09CaytKZ25ZNWNwMHM4ejNBNTBWR1dYamFjZVNrcU1jZGJVSXJQR21DSVRHUVlNMDRZckFYclJBU3hWZ0hNVXVMWThNVFM3c0w0REhyWUdGMVFDYldOWmxzR21WR0wvTU8yOVc0cHNGbms5Q0lWSXVsOVlnK1kiLCJtYWMiOiJkMzE4MjIxYmFlODEyODZmOTY2OWJjOWQ0M2M1NDQwNGQ4ZmI2MjNlM2RlYmQwMmZiZWUzNzYxZGYxNzQ4M2JlIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImE3OW9PNjdMc0hxTjdMMjcyN3hoWFE9PSIsInZhbHVlIjoiU2VIRGhrVENCeWRYZkpXOXZqbFEvWGFZcmdBdUYxalNpYWtuRTM3c3lJc1l6V25xVVFna1VpN21lRW14dEM2TE9OQ2ErdmFLekw0MjVWaG1VTnJDSERKdjQ5Q21GZ3FsTUV4RWtQZVQ2VUcyVHpyc3VTeTJ0NW9vY0dzckE0NEMiLCJtYWMiOiIwYTA1NTI1MmZjOTc5YmY2ZmU2Y2VlODlhN2I1MTY4YzI4MzNlZjY4ZWMzZTFjNzhmZTg0OGNmYTk4NjkxMTY1IiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:42:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571779260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1573054021 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/yLBXq2DWAd</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573054021\", {\"maxDepth\":0})</script>\n"}}