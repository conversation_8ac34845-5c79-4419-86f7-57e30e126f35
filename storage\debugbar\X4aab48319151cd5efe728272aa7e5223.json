{"__meta": {"id": "X4aab48319151cd5efe728272aa7e5223", "datetime": "2025-07-20 08:41:50", "utime": **********.629121, "method": "GET", "uri": "/dokumen-kapal/view/2NaDK50q4p", "ip": "127.0.0.1"}, "php": {"version": "8.1.10", "interface": "apache2handler"}, "messages": {"count": 2, "messages": [{"message": "[08:41:50] LOG.info: Mencari kapal dengan ID: 4", "message_html": null, "is_string": false, "label": "info", "time": **********.162109, "xdebug_link": null, "collector": "log"}, {"message": "[08:41:50] LOG.info: <PERSON><PERSON> di<PERSON>n {\n    \"kapal\": {\n        \"id\": 4,\n        \"nama\": \"TB. GEMA 201\",\n        \"rh_me_ps\": \"12623\",\n        \"rh_me_sb\": \"12618\",\n        \"jenis_kapal_id\": 1,\n        \"created_at\": \"2024-12-16T09:41:48.000000Z\",\n        \"updated_at\": \"2025-07-14T12:43:33.000000Z\",\n        \"gt_nt\": \"204\\/62\",\n        \"port_of_registry\": \"SAMARINDA\",\n        \"tanda_pendaftaran\": \"2022 IIk No. 9810\\/L\",\n        \"call_sign\": \"YDC 6210\",\n        \"imo\": \"9982706\",\n        \"mesin\": \"MITSUBISHI 2X 759 KW\",\n        \"tanda_selar\": \"GT. 204 No. 7085\\/IIK\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.166278, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752975709.841054, "end": **********.629172, "duration": 0.7881181240081787, "duration_str": "788ms", "measures": [{"label": "Booting", "start": 1752975709.841054, "relative_start": 0, "end": **********.080077, "relative_end": **********.080077, "duration": 0.23902297019958496, "duration_str": "239ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.080092, "relative_start": 0.23903799057006836, "end": **********.629176, "relative_end": 3.814697265625e-06, "duration": 0.549083948135376, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 27817304, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "dokumen.show", "param_count": null, "params": [], "start": **********.179579, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.phpdokumen.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.62235, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}, {"name": "layouts.header", "param_count": null, "params": [], "start": **********.623343, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/header.blade.phplayouts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "layouts.footer", "param_count": null, "params": [], "start": **********.624603, "type": "blade", "hash": "bladeC:\\laragon\\www\\sikapal\\resources\\views/layouts/footer.blade.phplayouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET dokumen-kapal/view/{kapal}", "middleware": "web, auth, Closure", "controller": "App\\Http\\Controllers\\DokumenKapalController@show", "namespace": null, "prefix": "/dokumen-kapal", "where": [], "as": "dokumen.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=54\" onclick=\"\">app/Http/Controllers/DokumenKapalController.php:54-70</a>"}, "queries": {"nb_statements": 3, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02679, "accumulated_duration_str": "26.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 17, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.123037, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.12801, "duration": 0.02504, "duration_str": "25.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "gema_kapal", "explain": null, "start_percent": 0, "width_percent": 93.468}, {"sql": "select * from `kapal` where `kapal`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.163027, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DokumenKapalController.php:63", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/DokumenKapalController.php", "file": "C:\\laragon\\www\\sikapal\\app\\Http\\Controllers\\DokumenKapalController.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FHttp%2FControllers%2FDokumenKapalController.php&line=63", "ajax": false, "filename": "DokumenKapalController.php", "line": "63"}, "connection": "gema_kapal", "explain": null, "start_percent": 93.468, "width_percent": 1.792}, {"sql": "select * from `dokumen_kapal` where `dokumen_kapal`.`kapal_id` = 4 and `dokumen_kapal`.`kapal_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 238}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\sikapal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.588489, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "dokumen.show:238", "source": {"index": 20, "namespace": "view", "name": "dokumen.show", "file": "C:\\laragon\\www\\sikapal\\resources\\views/dokumen/show.blade.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fresources%2Fviews%2Fdokumen%2Fshow.blade.php&line=238", "ajax": false, "filename": "show.blade.php", "line": "238"}, "connection": "gema_kapal", "explain": null, "start_percent": 95.259, "width_percent": 4.741}]}, "models": {"data": {"App\\Models\\DokumenKapal": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FDokumenKapal.php&line=1", "ajax": false, "filename": "DokumenKapal.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Kapal": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsikapal%2Fapp%2FModels%2FKapal.php&line=1", "ajax": false, "filename": "Kapal.php", "line": "?"}}}, "count": 25, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://sikapal.test/dokumen-kapal/view/2NaDK50q4p\"\n]"}, "request": {"path_info": "/dokumen-kapal/view/2NaDK50q4p", "status_code": "<pre class=sf-dump id=sf-dump-1138510794 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1138510794\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2049658697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2049658697\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-256851812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-256851812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1478595983 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">sikapal.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://sikapal.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjhRb0ZRM3IwczhpUnkyN0k0Tmx0OUE9PSIsInZhbHVlIjoiWUg0ak5jY1RHYlNPUlZZMUUwcDhJVEd4U3RycDN2ZHBYT0R3SlF6eGQvWXZ5MDN3RzRORjJ3aVY4V2dORmZCRlhFZXhVZEE5Yk5pSi9UM3JvZ2M0MjhCVWZ4TWZUTVZxeW1ycUk5ZDd6KzVPMlh6QXpqRFFibStPTmhFQmU1eVEiLCJtYWMiOiJjNGEyZDM2NDU2YWRjOWM4YjY2MmJlNzA0M2MxNzE0YjI0Y2M2NmJiYzc4YjJlZjg2YjYxNmM2OTJkOTY2ZDllIiwidGFnIjoiIn0%3D; sikapal_session=eyJpdiI6IkxvSTVBWGZDY20rbkpEYkNYcHRJUlE9PSIsInZhbHVlIjoiR2hYVVRjTUNOS293R2syRExMeWw0YXBoQWNlMmdkTVR2TVBwVlllUlo0QnFzblFKeldLYnJhYzlxTEN5aVVZUW1ReTJxOCtscUJRR3A4bUFjSjBpSEZndWM3N3BHWGFTNGl3VERaWWNvbTVnQlRhMkpKWUdrK0ZFVDYrT0lFcFciLCJtYWMiOiIwN2U3NDhlOWVkMmE1ZmY3ZmEwNmNmZWYxZmRjMzkzY2Y2ZDA0YTA2NGNiNGU5YzA2ZWE3YjgyYmU0ZjNkMWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478595983\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2059110287 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>sikapal_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z47ckW1sW3vByhJlsabzz4Jt9SLwE6fzIeTpobGt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059110287\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1675901772 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">max-age=0, must-revalidate, no-store, nocache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 01:41:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhBS1hDbmZBNmZ4VElBcThSakJmNEE9PSIsInZhbHVlIjoieTY3bVdkK0k1S3A3UTJxRFdKKzZmWWl2bjc5OEsrb2RDMGhuaEY0SFA4UHltT1FmS25sVVdieGxneGRkaXZGdTNHNzNVSDdrYVNYTzlMekN3RC8vVks0YUlSbEN6aVJTeXBXMnVvT2FWUmpyMzdyeHZYV3NyK05Pbk9yQjBkYVMiLCJtYWMiOiJlY2ZiYWY3ZmNlNzg1OTYyZmMwN2JhNzNhYTBmZjIzNTUwOTg0ODZhYzA3NGU1Mjc1YmI2NWVmNTliZTY2ZjZmIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:41:50 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">sikapal_session=eyJpdiI6ImJoUXU4TElmMlNvbVAya3RHTlRSaVE9PSIsInZhbHVlIjoiYmQwZXMraXhvbkRxdVBQM0N4STFZdXM5MTJGTnB4VEJOdDlxZkRlNVZzWFVvTjllV3JobStmNzNwLzllZHA3TEF4ODFJb0hJWEJkVkc0YjJYYVYyVHhHMHNTZWZIK2FndWpKTERvQktURnhaL2tCak9hTkJuWXNBV2NtWVhxbS8iLCJtYWMiOiJlZGJiOWU0NmZmYjBhZTU3MWE4ZjAxYjUxNDc4ODA4OWI3ZThmMDg2OWQ0ZTZhMjg3ZmMzZWU1MzljZGMwZGJkIiwidGFnIjoiIn0%3D; expires=Sun, 20 Jul 2025 03:41:50 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhBS1hDbmZBNmZ4VElBcThSakJmNEE9PSIsInZhbHVlIjoieTY3bVdkK0k1S3A3UTJxRFdKKzZmWWl2bjc5OEsrb2RDMGhuaEY0SFA4UHltT1FmS25sVVdieGxneGRkaXZGdTNHNzNVSDdrYVNYTzlMekN3RC8vVks0YUlSbEN6aVJTeXBXMnVvT2FWUmpyMzdyeHZYV3NyK05Pbk9yQjBkYVMiLCJtYWMiOiJlY2ZiYWY3ZmNlNzg1OTYyZmMwN2JhNzNhYTBmZjIzNTUwOTg0ODZhYzA3NGU1Mjc1YmI2NWVmNTliZTY2ZjZmIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:41:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">sikapal_session=eyJpdiI6ImJoUXU4TElmMlNvbVAya3RHTlRSaVE9PSIsInZhbHVlIjoiYmQwZXMraXhvbkRxdVBQM0N4STFZdXM5MTJGTnB4VEJOdDlxZkRlNVZzWFVvTjllV3JobStmNzNwLzllZHA3TEF4ODFJb0hJWEJkVkc0YjJYYVYyVHhHMHNTZWZIK2FndWpKTERvQktURnhaL2tCak9hTkJuWXNBV2NtWVhxbS8iLCJtYWMiOiJlZGJiOWU0NmZmYjBhZTU3MWE4ZjAxYjUxNDc4ODA4OWI3ZThmMDg2OWQ0ZTZhMjg3ZmMzZWU1MzljZGMwZGJkIiwidGFnIjoiIn0%3D; expires=Sun, 20-Jul-2025 03:41:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675901772\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1375479974 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2dbywr76KZG2F2ZSHQ6Ur4fpJplX3CgEZ9n04n5r</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://sikapal.test/dokumen-kapal/view/2NaDK50q4p</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375479974\", {\"maxDepth\":0})</script>\n"}}