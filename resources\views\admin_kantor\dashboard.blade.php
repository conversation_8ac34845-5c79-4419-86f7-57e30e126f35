@extends('layouts.app')

@section('content')
<div class="dashboard-wrapper p-4">
    <!-- Header Section dengan Glassmorphism Effect -->
    <div class="header-section mb-5 p-5 rounded-xl position-relative overflow-hidden backdrop-blur">
        <div class="position-relative z-2">
            <div class="d-flex align-items-center mb-4">
                <div class="greeting-icon me-3 glassmorphism">
                    <i class="fas fa-ship"></i>
                </div>
                <div>
                    <div class="d-flex align-items-center">
                        <h1 class="text-white mb-2 display-5 fw-bold">Dashboard Admin Kantor</h1>
                        <div class="badge bg-warning-soft text-warning ms-3 px-3 py-2">
                            <i class="fas fa-anchor me-1"></i>
                            TB/BG GEMA
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="online-indicator me-2"></div>
                        <p class="text-white-50 mb-0">Selamat datang kembali, <span class="fw-bold text-white">Admin!</span></p>
                    </div>
                </div>
            </div>
            <div class="mt-4 d-flex gap-3">
                <div class="quick-stats d-inline-flex align-items-center glassmorphism rounded-pill px-4 py-2">
                    <i class="fas fa-clock text-warning me-2"></i>
                    <span class="text-white">Update terakhir: <span id="lastUpdate">Hari ini, 14:30 WIB</span></span>
                </div>
                <div class="quick-stats d-inline-flex align-items-center glassmorphism rounded-pill px-4 py-2">
                    <i class="fas fa-calendar text-info me-2"></i>
                    <span class="text-white" id="currentDate">Loading...</span>
                </div>
            </div>
        </div>
        <div class="header-pattern"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>
    
    <!-- Stats Row -->
    <div class="row mb-5">
        <div class="col-md-3 mb-4">
            <div class="stats-card neumorphism rounded-xl p-4 position-relative overflow-hidden">
                <div class="position-relative z-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="stats-icon bg-primary-gradient">
                            <i class="fas fa-shopping-basket text-white"></i>
                        </div>
                        <div class="trend-badge {{ $persentasePembelian >= 0 ? 'up' : 'down' }}">
                            <i class="fas fa-arrow-{{ $persentasePembelian >= 0 ? 'up' : 'down' }}"></i>
                            {{ abs(round($persentasePembelian)) }}%
                        </div>
                    </div>
                    <h3 class="mb-1 display-6 fw-bold counter">{{ $totalPembelian }}</h3>
                    <p class="text-muted mb-0">Total Pembelian Kantor</p>
                    <div class="progress mt-3 neumorphism-inset" style="height: 4px;">
                        <div class="progress-bar bg-primary-gradient" style="width: {{ min(100, abs($persentasePembelian)) }}%"></div>
                    </div>
                </div>
                <div class="stats-pattern primary"></div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="stats-card neumorphism rounded-xl p-4 position-relative overflow-hidden">
                <div class="position-relative z-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="stats-icon bg-danger-gradient">
                            <i class="fas fa-file-invoice text-white"></i>
                        </div>
                        <div class="trend-badge {{ $persentasePengeluaran >= 0 ? 'up' : 'down' }}">
                            <i class="fas fa-arrow-{{ $persentasePengeluaran >= 0 ? 'up' : 'down' }}"></i>
                            {{ abs(round($persentasePengeluaran)) }}%
                        </div>
                    </div>
                    <h3 class="mb-1 display-6 fw-bold counter">{{ $totalPengeluaran }}</h3>
                    <p class="text-muted mb-0">Total Pengeluaran Kantor</p>
                    <div class="progress mt-3 neumorphism-inset" style="height: 4px;">
                        <div class="progress-bar bg-danger-gradient" style="width: {{ min(100, abs($persentasePengeluaran)) }}%"></div>
                    </div>
                </div>
                <div class="stats-pattern danger"></div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="stats-card neumorphism rounded-xl p-4 position-relative overflow-hidden">
                <div class="position-relative z-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="stats-icon bg-success-gradient">
                            <i class="fas fa-receipt text-white"></i>
                        </div>
                        <div class="trend-badge {{ $persentaseTransaksi >= 0 ? 'up' : 'down' }}">
                            <i class="fas fa-arrow-{{ $persentaseTransaksi >= 0 ? 'up' : 'down' }}"></i>
                            {{ abs(round($persentaseTransaksi)) }}%
                        </div>
                    </div>
                    <h3 class="mb-1 display-6 fw-bold counter">{{ $transaksiHariIni }}</h3>
                    <p class="text-muted mb-0">Transaksi Hari Ini</p>
                    <div class="progress mt-3 neumorphism-inset" style="height: 4px;">
                        <div class="progress-bar bg-success-gradient" style="width: {{ min(100, abs($persentaseTransaksi)) }}%"></div>
                    </div>
                </div>
                <div class="stats-pattern success"></div>
            </div>
        </div>

        <!-- Card Jumlah Sparepart -->
        <div class="col-md-3 mb-4">
            <div class="stats-card neumorphism rounded-xl p-4 position-relative overflow-hidden">
                <div class="position-relative z-2">
                    <div class="d-flex justify-content-between mb-3">
                        <div class="stats-icon bg-info-gradient">
                            <i class="fas fa-tools text-white"></i>
                        </div>
                        <div class="trend-badge {{ $persentaseSparepart >= 0 ? 'up' : 'down' }}">
                            <i class="fas fa-arrow-{{ $persentaseSparepart >= 0 ? 'up' : 'down' }}"></i>
                            {{ abs(round($persentaseSparepart)) }}%
                        </div>
                    </div>
                    <h3 class="mb-1 display-6 fw-bold counter">{{ $totalSparepart }}</h3>
                    <p class="text-muted mb-0">Total Data Sparepart</p>
                    <div class="progress mt-3 neumorphism-inset" style="height: 4px;">
                        <div class="progress-bar bg-info-gradient" style="width: {{ min(100, abs($persentaseSparepart)) }}%"></div>
                    </div>
                </div>
                <div class="stats-pattern info"></div>
            </div>
        </div>
    </div>

    @if($jumlahKapalStokKosong > 0)
    <div class="card modern-card mb-4">
        <div class="card-header bg-primary-dark text-white d-flex align-items-center">
            <div class="d-flex align-items-center flex-grow-1">
                <div class="alert-icon me-3">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h5 class="mb-0">Informasi Stok Sparepart Kapal</h5>
                    <small>Terdapat {{ $jumlahKapalStokKosong }} jenis sparepart yang perlu diklaim oleh kapal</small>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="stokKapalKosongTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Kapal</th>
                            <th>Nama Barang</th>
                            <th>No. Seri</th>
                            <th>Jenis</th>
                            <th>Satuan</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($stokKapalKosong as $index => $item)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>
                                    <span class="badge bg-{{ $item->jenis_kapal_id == 1 ? 'primary' : 'success' }}">
                                        {{ $item->nama_kapal }}
                                    </span>
                                </td>
                                <td>{{ $item->nama_barang }}</td>
                                <td>{{ $item->nomor_seri }}</td>
                                <td>{{ $item->jenis }}</td>
                                <td>{{ $item->satuan }}</td>
                                <td>
                                    @if($item->jumlah_pengeluaran > 0)
                                        <span class="badge bg-info">
                                            Menunggu Klaim ({{ $item->jumlah_pengeluaran }} unit)
                                        </span>
                                    @else
                                        <span class="badge bg-danger">
                                            Stok Habis
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif

    <!-- Menu Cards dengan Modern Design -->
    <div class="section-header d-flex align-items-center mb-4">
        <div class="section-icon me-3">
            <i class="fas fa-compass text-primary"></i>
        </div>
        <h5 class="text-dark mb-0">Menu Utama</h5>
    </div>
    
    <div class="row g-4">
        <!-- Card Pembelian Kantor -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-primary-soft mb-3">
                                <i class="fas fa-tools text-primary"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">PEMBELIAN AMPRAHAN SUKU CADANG</span>
                                        @if($pembelianBaru > 0)
                                            <span class="badge bg-primary-soft text-primary ms-2">{{ $pembelianBaru }} Baru</span>
                                        @endif
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-cog text-primary"></i>
                                            <span>Suku Cadang Mesin</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-clipboard-list text-primary"></i>
                                            <span>Daftar Amprahan</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-file-invoice text-primary"></i>
                                            <span>Laporan Pembelian</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('pembelian-kantor.index') }}" class="btn btn-primary w-100 btn-modern">
                                        <span>Kelola Amprahan</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Riwayat Pembelian - HIDDEN -->
        {{-- <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-info-soft mb-3">
                                <i class="fas fa-history text-info"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">RIWAYAT PEMBELIAN</span>
                                        <span class="badge bg-info-soft text-info ms-2">History</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-clock text-info"></i>
                                            <span>Riwayat Transaksi</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-chart-line text-info"></i>
                                            <span>Tracking Stok</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-file-alt text-info"></i>
                                            <span>Laporan Riwayat</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('riwayat-pembelian.index') }}" class="btn btn-info w-100 btn-modern">
                                        <span>Lihat Riwayat</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Card Pengeluaran Kantor -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-danger-soft mb-3">
                                <i class="fas fa-file-invoice-dollar text-danger"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">PENGELUARAN AMPRAHAN SUKU CADANG</span>
                                        @if($pengeluaranBaru > 0)
                                            <span class="badge bg-danger-soft text-danger ms-2">{{ $pengeluaranBaru }} Baru</span>
                                        @endif
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-tools text-danger"></i>
                                            <span>Pengeluaran Suku Cadang</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-clipboard-check text-danger"></i>
                                            <span>Verifikasi Amprahan</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-chart-line text-danger"></i>
                                            <span>Laporan Pengeluaran</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('pengeluaran-kantor.index') }}" class="btn btn-danger w-100 btn-modern">
                                        <span>Kelola Pengeluaran</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Data Sparepart -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-success-soft mb-3">
                                <i class="fas fa-boxes text-success"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">DATA SPAREPART</span>
                                        <span class="badge bg-success-soft text-success ms-2">Master</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-database text-success"></i>
                                            <span>Master Data Sparepart</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-tags text-success"></i>
                                            <span>Katalog Suku Cadang</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-list-alt text-success"></i>
                                            <span>Manajemen Inventori</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('sparepart.index') }}" class="btn btn-success w-100 btn-modern">
                                        <span>Kelola Sparepart</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Export Data -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-info-soft mb-3">
                                <i class="fas fa-file-excel text-info"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">EXPORT DATA SPAREPART</span>
                                        <span class="badge bg-info-soft text-info ms-2">Excel</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-boxes text-info"></i>
                                            <span>Data Stok Sparepart</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-file-invoice text-info"></i>
                                            <span>Data Amprahan</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-history text-info"></i>
                                            <span>Riwayat Transaksi</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('stok-sparepart.export-form') }}" class="btn btn-info w-100 btn-modern">
                                        <span>Export Data</span>
                                        <i class="fas fa-file-export ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row for Additional Menus -->
    <div class="row g-4 mt-2">
        <!-- Card Crew Management -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-purple-soft mb-3">
                                <i class="fas fa-users text-purple"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">MANAJEMEN CREW</span>
                                        <span class="badge bg-purple-soft text-purple ms-2">Crew</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-user-plus text-purple"></i>
                                            <span>Daftar Crew</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-id-card text-purple"></i>
                                            <span>Data Ijazah</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-calendar-check text-purple"></i>
                                            <span>Masa Berlaku Buku Pelaut</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('crew.index') }}" class="btn btn-purple w-100 btn-modern">
                                        <span>Kelola Crew</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Voyage Report -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-teal-soft mb-3">
                                <i class="fas fa-ship text-teal"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">VOYAGE REPORT KAPAL</span>
                                        <span class="badge bg-teal-soft text-teal ms-2">Report</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-file-alt text-teal"></i>
                                            <span>Laporan Perjalanan</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-clock text-teal"></i>
                                            <span>Waktu Operasional</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-download text-teal"></i>
                                            <span>Lampiran Dokumen</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('voyage-report.index') }}" class="btn btn-teal w-100 btn-modern">
                                        <span>Kelola Voyage Report</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Dokumen Kapal -->
        <div class="col-12 col-md-3">
            <div class="menu-card modern-card h-100">
                <div class="card-body p-4">
                    <div class="row h-100">
                        <div class="col-auto">
                            <div class="feature-icon-wrapper bg-orange-soft mb-3">
                                <i class="fas fa-folder-open text-orange"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex flex-column h-100">
                                <div>
                                    <h5 class="card-title d-flex align-items-center mb-3">
                                        <span class="text-wrap">DOKUMEN KAPAL</span>
                                        <span class="badge bg-orange-soft text-orange ms-2">Dokumen</span>
                                    </h5>
                                    <div class="feature-list mb-4">
                                        <div class="feature-item">
                                            <i class="fas fa-ship text-orange"></i>
                                            <span>Tugboat & Tongkang</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-upload text-orange"></i>
                                            <span>Upload Dokumen</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-certificate text-orange"></i>
                                            <span>Sertifikat Kapal</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-auto">
                                    <a href="{{ route('admin.kantor.kapal') }}" class="btn btn-orange w-100 btn-modern">
                                        <span>Kelola Dokumen</span>
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Glassmorphism Effect */
.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Neumorphism Effect */
.neumorphism {
    background: #ffffff;
    box-shadow: 10px 10px 20px #d1d9e6,
                -10px -10px 20px #ffffff;
    border: none;
}

.neumorphism-inset {
    box-shadow: inset 2px 2px 5px #d1d9e6,
                inset -2px -2px 5px #ffffff;
}

/* Floating Shapes Animation */
.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 15s infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: -50px;
    right: 10%;
}

.shape-2 {
    width: 150px;
    height: 150px;
    bottom: -70px;
    left: 20%;
    animation-delay: 3s;
}

.shape-3 {
    width: 70px;
    height: 70px;
    top: 50%;
    right: 30%;
    animation-delay: 5s;
}

@keyframes float {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
    100% { transform: translateY(0) rotate(360deg); }
}

/* Online Indicator */
.online-indicator {
    width: 8px;
    height: 8px;
    background: #2dce89;
    border-radius: 50%;
    position: relative;
}

.online-indicator::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: inherit;
    animation: pulse-ring 2s ease infinite;
}

/* Button Hover Effect */
.btn-hover-translate {
    transition: all 0.3s ease;
}

.btn-hover-translate:hover {
    transform: translateX(5px);
}

/* Counter Animation */
.counter {
    opacity: 0;
    animation: countUp 1s ease forwards;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Icon */
.section-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Additional Styles */
.header-section {
    background: linear-gradient(135deg, #2937f0 0%, #9f1ae2 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header-section {
        padding: 3rem 2rem !important;
    }
    
    .quick-stats {
        font-size: 0.875rem;
    }
}

/* Modern Card Styles */
.modern-card {
    background: #ffffff;
    border-radius: 16px;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 35px rgba(0, 0, 0, 0.08);
}

/* Feature Icon Styles */
.feature-icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.bg-primary-soft {
    background-color: rgba(41, 55, 240, 0.1);
}

.bg-danger-soft {
    background-color: rgba(242, 87, 103, 0.1);
}

/* Feature List Styles */
.feature-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    color: #6c757d;
}

.feature-item i {
    font-size: 1rem;
}

/* Modern Button Style */
.btn-modern {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateX(5px);
}

.btn-modern i {
    transition: transform 0.3s ease;
}

.btn-modern:hover i {
    transform: translateX(3px);
}

/* Badge Styles */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    font-size: 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .feature-icon-wrapper {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .card-title {
        font-size: 1.1rem;
    }

    .feature-item {
        font-size: 0.9rem;
    }

    .btn-modern {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .modern-card {
        margin-bottom: 1rem;
    }

    .feature-list {
        gap: 0.5rem;
    }

    .card-body {
        padding: 1.25rem !important;
    }
}

/* Card Title Styles */
.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.4;
}

/* Feature Item Styles */
.feature-item {
    color: #4a5568;
}

/* Responsive adjustments for long titles */
@media (max-width: 768px) {
    .card-title {
        font-size: 1rem;
    }
    
    .badge {
        font-size: 0.7rem;
    }
}

.greeting-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: #ffffff;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.bg-warning-soft {
    background-color: rgba(255, 193, 7, 0.15);
}

.badge {
    font-size: 0.85rem;
    font-weight: 500;
}

.bg-success-soft {
    background-color: rgba(45, 206, 137, 0.1);
}

.text-success {
    color: #2dce89 !important;
}

.alert-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.alert-modern .alert-content {
    display: flex;
    align-items: center;
    padding: 1rem;
}

.alert-modern .alert-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 193, 7, 0.2);
    border-radius: 12px;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: #ffc107;
}

.alert-modern .alert-message {
    flex-grow: 1;
}

.alert-modern .alert-heading {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #2d3748;
}

.alert-modern p {
    color: #4a5568;
}

#stokKosongTable {
    width: 100%;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

.badge {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
}

/* Card Warning Style */
.card-header.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border: none;
}

.alert-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    font-size: 1.2rem;
}

#stokKosongTable thead th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
}

#stokKosongTable tbody td {
    vertical-align: middle;
    padding: 1rem;
}

.btn-sm {
    padding: 0.4rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Custom Background Colors */
.bg-primary-dark {
    background: #1a237e !important; /* Biru gelap */
}

/* Card Header Styles */
.card-header.bg-primary-dark {
    border: none;
    background: linear-gradient(135deg, #1a237e, #283593) !important;
    box-shadow: 0 4px 20px rgba(26, 35, 126, 0.1);
}

.card-header.bg-primary-dark .alert-icon {
    background: rgba(255, 255, 255, 0.2);
}

.card-header.bg-primary-dark small {
    opacity: 0.9;
}

/* Purple Color Scheme */
.bg-purple-soft {
    background-color: rgba(156, 39, 176, 0.1) !important;
}

.text-purple {
    color: #9c27b0 !important;
}

.btn-purple {
    background: linear-gradient(135deg, #9c27b0, #e91e63);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-purple:hover {
    background: linear-gradient(135deg, #7b1fa2, #c2185b);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
}

/* Teal Color Scheme */
.bg-teal-soft {
    background-color: rgba(0, 150, 136, 0.1) !important;
}

.text-teal {
    color: #009688 !important;
}

.btn-teal {
    background: linear-gradient(135deg, #009688, #00bcd4);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-teal:hover {
    background: linear-gradient(135deg, #00796b, #0097a7);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 150, 136, 0.3);
}

/* Info Color Scheme */
.bg-info-soft {
    background-color: rgba(13, 202, 240, 0.1) !important;
}

.text-info {
    color: #0dcaf0 !important;
}

.btn-info {
    background: linear-gradient(135deg, #0dcaf0, #17a2b8);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-info:hover {
    background: linear-gradient(135deg, #0bb5d3, #138496);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 202, 240, 0.3);
    color: white;
}

/* Orange Color Scheme */
.bg-orange-soft {
    background-color: rgba(255, 152, 0, 0.1) !important;
}

.text-orange {
    color: #ff9800 !important;
}

.btn-orange {
    background: linear-gradient(135deg, #ff9800, #ff5722);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-orange:hover {
    background: linear-gradient(135deg, #f57c00, #e64a19);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
}
</style>

<script>
// Update current date
function updateDate() {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    const currentDate = new Date().toLocaleDateString('id-ID', options);
    document.getElementById('currentDate').textContent = currentDate;
}

// Counter animation
function animateCounter() {
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        let count = 0;
        const duration = 2000;
        const increment = target / (duration / 16);
        
        const updateCount = () => {
            count += increment;
            counter.textContent = Math.round(count);
            if(count < target) {
                requestAnimationFrame(updateCount);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCount();
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateDate();
    animateCounter();
});

function updateLastUpdate() {
    const now = new Date();
    const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    
    let hours = now.getHours();
    let minutes = now.getMinutes();
    
    // Menambahkan 0 di depan angka jika kurang dari 10
    hours = hours < 10 ? '0' + hours : hours;
    minutes = minutes < 10 ? '0' + minutes : minutes;
    
    const timeString = `${days[now.getDay()]}, ${hours}:${minutes} WIB`;
    document.getElementById('lastUpdate').textContent = timeString;
}

// Update setiap 1 menit
document.addEventListener('DOMContentLoaded', function() {
    updateLastUpdate();
    setInterval(updateLastUpdate, 60000);
});

$(document).ready(function() {
    $('#stokKosongTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json'
        },
        pageLength: 5,
        order: [[0, 'asc']],
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
    });
});
</script>
@endsection 