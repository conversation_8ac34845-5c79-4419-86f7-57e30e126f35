<?php

namespace App\Http\Controllers;

use App\Models\DokumenKapal;
use App\Models\Kapal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Helpers\HashIdHelper;
use PDF;
use Carbon\Carbon;

class DokumenKapalController extends Controller
{
    public function __construct()
    {
        Carbon::setLocale('id');

        // Pastikan hanya admin kantor yang bisa mengakses
        $this->middleware(function ($request, $next) {
            if (!auth()->check() || !auth()->user()->isAdminKantor()) {
                if ($request->expectsJson()) {
                    return response()->json(['error' => 'Unauthorized. Hanya admin kantor yang dapat mengakses fitur ini.'], 403);
                }
                abort(403, 'Unauthorized. Hanya admin kantor yang dapat mengakses fitur ini.');
            }
            return $next($request);
        });
    }

    public function show($hash)
    {
        try {
            $id = HashIdHelper::decode($hash);
            if (!$id) {
                throw new \Exception('Invalid hash');
            }
            
            Log::info('Mencari kapal dengan ID: ' . $id);
            $kapal = Kapal::findOrFail($id);
            Log::info('Kapal ditemukan', ['kapal' => $kapal]);
            return view('dokumen.show', compact('kapal'));
        } catch (\Exception $e) {
            Log::error('Error saat mencari kapal: ' . $e->getMessage());
            abort(404, 'Kapal tidak ditemukan');
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'dokumen' => 'required|mimes:pdf|max:25600',
            'kapal_id' => 'required|exists:kapal,id',
            'sertifikat_status' => 'nullable|string',
            'diberikan_di' => 'nullable|string',
            'tanggal_diberikan' => 'nullable|date',
            'berlaku_sampai' => 'nullable|date|required_if:berlaku_selamanya,0',
            'berlaku_selamanya' => 'nullable|boolean',
            'check_list_captain' => 'nullable|boolean',
            'check_list_chief' => 'nullable|boolean',
            'is_endorsed' => 'nullable|boolean',
            'tanggal_endorse' => 'nullable|date|required_if:is_endorsed,1'
        ]);

        $file = $request->file('dokumen');
        $fileName = time() . '_' . $file->getClientOriginalName();
        
        $path = $file->storeAs('public/dokumen_kapal', $fileName);

        $dokumen = DokumenKapal::create([
            'kapal_id' => $request->kapal_id,
            'nama_file' => $file->getClientOriginalName(),
            'file_path' => 'dokumen_kapal/' . $fileName,
            'sertifikat_status' => $request->sertifikat_status,
            'diberikan_di' => $request->diberikan_di,
            'tanggal_diberikan' => $request->tanggal_diberikan,
            'berlaku_sampai' => $request->berlaku_selamanya ? null : $request->berlaku_sampai,
            'berlaku_selamanya' => $request->berlaku_selamanya ?? false,
            'check_list_captain' => $request->check_list_captain ?? false,
            'check_list_chief' => $request->check_list_chief ?? false,
            'is_endorsed' => $request->is_endorsed ?? false,
            'tanggal_endorse' => $request->is_endorsed ? $request->tanggal_endorse : null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Dokumen berhasil diupload',
            'dokumen' => [
                'id' => $dokumen->id,
                'nama_file' => $dokumen->nama_file,
                'file_path' => Storage::url($dokumen->file_path),
                'sertifikat_status' => $dokumen->sertifikat_status,
                'diberikan_di' => $dokumen->diberikan_di,
                'tanggal_diberikan' => $dokumen->tanggal_diberikan?->format('d/m/Y'),
                'berlaku_sampai' => $dokumen->berlaku_sampai?->format('d/m/Y'),
                'berlaku_selamanya' => $dokumen->berlaku_selamanya,
                'keterangan' => $dokumen->keterangan,
                'check_list_captain' => $dokumen->check_list_captain,
                'check_list_chief' => $dokumen->check_list_chief,
                'is_endorsed' => $dokumen->is_endorsed,
                'tanggal_endorse' => $dokumen->tanggal_endorse?->format('d/m/Y'),
                'durasi_endorse' => $dokumen->durasi_endorse,
                'durasi_berlaku' => $dokumen->durasi_berlaku,
                'sisa_waktu_berlaku' => $dokumen->sisa_waktu_berlaku
            ]
        ]);
    }

    public function destroy($id)
    {
        $dokumen = DokumenKapal::findOrFail($id);
        Storage::delete('public/' . $dokumen->file_path);
        $dokumen->delete();

        return response()->json([
            'success' => true,
            'message' => 'Dokumen berhasil dihapus'
        ]);
    }

    public function detail($id)
    {
        $dokumen = DokumenKapal::findOrFail($id);
        
        return response()->json([
            'success' => true,
            'dokumen' => [
                'id' => $dokumen->id,
                'nama_file' => $dokumen->nama_file,
                'file_path' => Storage::url($dokumen->file_path),
                'sertifikat_status' => $dokumen->sertifikat_status,
                'diberikan_di' => $dokumen->diberikan_di,
                'tanggal_diberikan' => $dokumen->tanggal_diberikan?->format('Y-m-d'),
                'berlaku_sampai' => $dokumen->berlaku_sampai?->format('Y-m-d'),
                'berlaku_selamanya' => $dokumen->berlaku_selamanya,
                'check_list_captain' => $dokumen->check_list_captain,
                'check_list_chief' => $dokumen->check_list_chief,
                'is_endorsed' => $dokumen->is_endorsed,
                'tanggal_endorse' => $dokumen->tanggal_endorse?->format('Y-m-d'),
                'durasi_endorse' => $dokumen->durasi_endorse,
                'durasi_berlaku' => $dokumen->durasi_berlaku,
                'sisa_waktu_berlaku' => $dokumen->sisa_waktu_berlaku
            ]
        ]);
    }

    public function update(Request $request, $id)
    {
        $dokumen = DokumenKapal::findOrFail($id);
        
        $request->validate([
            'dokumen' => 'nullable|mimes:pdf|max:25600',
            'sertifikat_status' => 'nullable|string',
            'diberikan_di' => 'nullable|string',
            'tanggal_diberikan' => 'nullable|date',
            'berlaku_sampai' => 'nullable|date|required_if:berlaku_selamanya,0',
            'berlaku_selamanya' => 'nullable|boolean',
            'check_list_captain' => 'nullable|boolean',
            'check_list_chief' => 'nullable|boolean',
            'is_endorsed' => 'nullable|boolean',
            'tanggal_endorse' => 'nullable|date|required_if:is_endorsed,1'
        ]);
        
        // Handle file upload if new file is provided
        if ($request->hasFile('dokumen')) {
            try {
                $file = $request->file('dokumen');
                
                // Generate filename
                $fileName = time() . '_' . $file->getClientOriginalName();
                
                // Delete old file if exists
                if ($dokumen->file_path && Storage::exists('public/' . $dokumen->file_path)) {
                    Storage::delete('public/' . $dokumen->file_path);
                }
                
                // Store new file
                $file->storeAs('public/dokumen_kapal', $fileName);
                
                // Update database
                $dokumen->nama_file = $file->getClientOriginalName();
                $dokumen->file_path = 'dokumen_kapal/' . $fileName;
                
                Log::info('File upload success', [
                    'file_name' => $fileName,
                    'file_path' => $dokumen->file_path
                ]);
            } catch (\Exception $e) {
                Log::error('File upload failed: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal mengupload file: ' . $e->getMessage()
                ], 500);
            }
        }
        
        // Update other fields
        $dokumen->sertifikat_status = $request->sertifikat_status;
        $dokumen->diberikan_di = $request->diberikan_di;
        $dokumen->tanggal_diberikan = $request->tanggal_diberikan;
        $dokumen->berlaku_sampai = $request->berlaku_selamanya ? null : $request->berlaku_sampai;
        $dokumen->berlaku_selamanya = $request->berlaku_selamanya ?? false;
        $dokumen->check_list_captain = $request->check_list_captain ?? false;
        $dokumen->check_list_chief = $request->check_list_chief ?? false;
        $dokumen->is_endorsed = $request->is_endorsed ?? false;
        $dokumen->tanggal_endorse = $request->is_endorsed ? $request->tanggal_endorse : null;
        
        try {
            $dokumen->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Dokumen berhasil diperbarui',
                'dokumen' => [
                    'file_path' => Storage::url($dokumen->file_path),
                    'nama_file' => $dokumen->nama_file
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Save document failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan dokumen: ' . $e->getMessage()
            ], 500);
        }
    }

    public function toggleChecklist(Request $request, $id)
    {
        $dokumen = DokumenKapal::findOrFail($id);
        
        $request->validate([
            'type' => 'required|in:captain,chief'
        ]);
        
        $type = $request->type;
        
        if ($type === 'captain') {
            $dokumen->check_list_captain = !$dokumen->check_list_captain;
        } else {
            $dokumen->check_list_chief = !$dokumen->check_list_chief;
        }
        
        $dokumen->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Checklist berhasil diupdate',
            'status' => [
                'captain' => $dokumen->check_list_captain,
                'chief' => $dokumen->check_list_chief
            ]
        ]);
    }

    public function downloadList(Kapal $kapal)
    {
        // Tentukan kapal1 (Tugboat) dan kapal2 (Tongkang)
        $kapal1 = null;
        $kapal2 = null;
        
        // Jika kapal saat ini adalah Tugboat
        if ($kapal->jenis_kapal_id == 1) {
            $kapal1 = $kapal;
            // Cari Tongkang yang terkait
            $kapal2 = Kapal::where('jenis_kapal_id', 2)->first();
        } 
        // Jika kapal saat ini adalah Tongkang
        else {
            // Cari Tugboat
            $kapal1 = Kapal::where('jenis_kapal_id', 1)->first();
            $kapal2 = $kapal;
        }

        // Ambil dokumen untuk Tugboat
        $dokumenKapal1 = collect([]);
        if ($kapal1) {
            $dokumenKapal1 = $kapal1->dokumen()
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($dok) {
                    if ($dok->tanggal_diberikan) {
                        $dok->tanggal_diberikan = $dok->tanggal_diberikan->locale('id');
                    }
                    if ($dok->berlaku_sampai) {
                        $dok->berlaku_sampai = $dok->berlaku_sampai->locale('id');
                    }
                    if ($dok->tanggal_endorse) {
                        $dok->tanggal_endorse = $dok->tanggal_endorse->locale('id');
                    }
                    return $dok;
                });
        }

        // Ambil dokumen untuk Tongkang
        $dokumenKapal2 = collect([]);
        if ($kapal2) {
            $dokumenKapal2 = $kapal2->dokumen()
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($dok) {
                    if ($dok->tanggal_diberikan) {
                        $dok->tanggal_diberikan = $dok->tanggal_diberikan->locale('id');
                    }
                    if ($dok->berlaku_sampai) {
                        $dok->berlaku_sampai = $dok->berlaku_sampai->locale('id');
                    }
                    if ($dok->tanggal_endorse) {
                        $dok->tanggal_endorse = $dok->tanggal_endorse->locale('id');
                    }
                    return $dok;
                });
        }

        $pdf = PDF::loadView('dokumen.pdf.list', [
            'kapal1' => $kapal1,
            'kapal2' => $kapal2,
            'dokumenKapal1' => $dokumenKapal1,
            'dokumenKapal2' => $dokumenKapal2,
            'PAGE_NUM' => '{PAGE_NUM}',
            'PAGE_COUNT' => '{PAGE_COUNT}'
        ]);
        
        $pdf->getDomPDF()->set_option("enable_php", true);
        
        return $pdf->stream('dokumen.pdf');
    }
}
